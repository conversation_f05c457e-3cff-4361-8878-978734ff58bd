cmake_minimum_required(VERSION 3.16)
project(pastewith)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找依赖库
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)

if(WIN32)
    # Windows 依赖
    target_link_libraries(pastewith wininet)
else()
    # Linux 依赖
    find_package(CURL REQUIRED)
    pkg_check_modules(X11 REQUIRED x11)
    
    target_link_libraries(pastewith 
        ${CURL_LIBRARIES}
        ${X11_LIBRARIES}
        OpenSSL::SSL 
        OpenSSL::Crypto
    )
    
    target_include_directories(pastewith PRIVATE 
        ${CURL_INCLUDE_DIRS}
        ${X11_INCLUDE_DIRS}
    )
endif()

# 添加可执行文件
add_executable(pastewith pastewith.cpp)

# 链接 OpenSSL
target_link_libraries(pastewith OpenSSL::SSL OpenSSL::Crypto)

# 编译选项
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(pastewith PRIVATE -Wall -Wextra)
endif()
