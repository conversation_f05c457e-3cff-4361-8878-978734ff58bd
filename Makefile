CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2

# 检测操作系统
UNAME_S := $(shell uname -s)

# 源文件
SOURCES = pastewith.cpp
TARGET = pastewith

# 基础库
LIBS = -lssl -lcrypto -lcurl

ifeq ($(UNAME_S),Linux)
    # Linux 特定库
    LIBS += -lX11
endif

ifeq ($(UNAME_S),Darwin)
    # macOS 特定库 (可能需要额外配置)
    LIBS += -framework ApplicationServices
endif

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCES) $(LIBS)

# 清理
clean:
	rm -f $(TARGET)

# 安装依赖 (Ubuntu/Debian)
install-deps-ubuntu:
	sudo apt-get update
	sudo apt-get install -y libcurl4-openssl-dev libssl-dev libx11-dev

# 安装依赖 (CentOS/RHEL/Fedora)
install-deps-fedora:
	sudo dnf install -y libcurl-devel openssl-devel libX11-devel

# 安装依赖 (macOS)
install-deps-macos:
	brew install curl openssl

.PHONY: all clean install-deps-ubuntu install-deps-fedora install-deps-macos
