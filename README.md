# PasteWith - 剪贴板监控工具

这个项目提供了三种语言实现的剪贴板监控工具，可以自动检测剪贴板中的图片和文档文件，并上传到指定服务器。

## 功能特性

- 监控剪贴板变化
- 自动检测并上传图片文件
- 自动检测并上传文档文件（docx, doc, ppt, pptx, xls, xlsx, pdf）
- 支持 file:// 协议路径解析
- MD5 哈希值计算避免重复上传
- 跨平台支持

## 实现版本

### 1. Python 版本 (pastewith_pyperclip.py)

**依赖:**
```bash
pip install pyperclip pillow requests
```

**运行:**
```bash
python3 pastewith_pyperclip.py
```

### 2. Go 版本 (pastewith.go)

**依赖安装:**
```bash
go mod tidy
```

**编译:**
```bash
go build -o pastewith pastewith.go
```

**运行:**
```bash
./pastewith
```

### 3. C++ 版本 (pastewith.cpp)

**依赖安装:**

Ubuntu/Debian:
```bash
make install-deps-ubuntu
```

CentOS/RHEL/Fedora:
```bash
make install-deps-fedora
```

macOS:
```bash
make install-deps-macos
```

**编译方式1 - 使用 Makefile:**
```bash
make
```

**编译方式2 - 使用 CMake:**
```bash
mkdir build
cd build
cmake ..
make
```

**运行:**
```bash
./pastewith
```

## 配置

在每个实现中修改以下常量来配置服务器地址：

```
SERVER_IP = "************"
SERVER_PORT = "12139"
```

## 支持的文件格式

- 文档: docx, doc, ppt, pptx, xls, xlsx, pdf
- 图片: png, jpg, jpeg, gif, bmp (通过剪贴板截图)

## 工作原理

1. **文本监控**: 监控剪贴板文本变化，解析其中的文件路径
2. **图片监控**: 监控剪贴板中的图片数据
3. **文件验证**: 检查文件是否存在且为支持的格式
4. **去重处理**: 使用 MD5 哈希值避免重复上传
5. **自动上传**: 将文件上传到配置的服务器

## 平台特定说明

### Windows
- C++ 版本使用 WinINet API 进行 HTTP 请求
- 剪贴板访问使用 Windows Clipboard API

### Linux
- C++ 版本使用 libcurl 进行 HTTP 请求
- 剪贴板访问使用 X11 API
- 需要在 X11 环境下运行

### macOS
- 类似 Linux，但可能需要额外的权限设置

## 注意事项

1. **图片剪贴板**: Go 和 C++ 版本的图片剪贴板监控需要平台特定的实现，目前提供了基础框架
2. **权限**: 某些系统可能需要授予剪贴板访问权限
3. **网络**: 确保能够访问配置的服务器地址
4. **中文路径**: 所有版本都处理了中文文件路径的问题

## 开发说明

- Python 版本功能最完整，可作为参考实现
- Go 版本提供了良好的跨平台支持和性能
- C++ 版本提供了最佳的性能和系统集成

## 故障排除

1. **编译错误**: 确保安装了所有依赖库
2. **运行时错误**: 检查服务器地址和端口配置
3. **权限问题**: 确保程序有访问剪贴板的权限
4. **网络问题**: 检查防火墙和网络连接

## 许可证

本项目采用 MIT 许可证。
