@echo off
setlocal enabledelayedexpansion

echo === PasteWith 构建脚本 (Windows) ===

:menu
echo.
echo 请选择要构建的版本:
echo 1) Go 版本
echo 2) C++ 版本 (MinGW)
echo 3) 检查 Python 版本
echo 4) 构建所有版本
echo 5) 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto build_go
if "%choice%"=="2" goto build_cpp
if "%choice%"=="3" goto check_python
if "%choice%"=="4" goto build_all
if "%choice%"=="5" goto exit
echo 无效选择，请输入 1-5
goto menu

:build_go
echo === 构建 Go 版本 ===
where go >nul 2>nul
if errorlevel 1 (
    echo 错误: 未找到 Go 编译器
    echo 请安装 Go: https://golang.org/dl/
    goto menu
)

echo Go 版本:
go version

echo 下载依赖...
go mod tidy

echo 编译...
go build -o pastewith-go.exe pastewith.go

echo Go 版本构建完成: pastewith-go.exe
goto menu

:build_cpp
echo === 构建 C++ 版本 ===
where g++ >nul 2>nul
if errorlevel 1 (
    echo 错误: 未找到 g++ 编译器
    echo 请安装 MinGW-w64 或 MSYS2
    echo 下载地址: https://www.mingw-w64.org/downloads/
    goto menu
)

echo g++ 版本:
g++ --version | findstr g++

echo 编译...
g++ -std=c++17 -Wall -Wextra -O2 -DWINDOWS -o pastewith.exe pastewith.cpp -lwininet -lws2_32 -lssl -lcrypto

if errorlevel 1 (
    echo 编译失败，请检查依赖库是否安装
    echo 需要 OpenSSL 库支持
    goto menu
)

echo C++ 版本构建完成: pastewith.exe
goto menu

:check_python
echo === 检查 Python 版本 ===
where python >nul 2>nul
if errorlevel 1 (
    echo 错误: 未找到 Python
    echo 请安装 Python: https://www.python.org/downloads/
    goto menu
)

echo Python 版本:
python --version

echo 检查 Python 依赖...
python -c "import pyperclip, PIL, requests" 2>nul
if errorlevel 1 (
    echo 缺少 Python 依赖，正在安装...
    pip install pyperclip pillow requests
)

echo Python 版本就绪: pastewith_pyperclip.py
goto menu

:build_all
echo === 构建所有版本 ===
call :check_python
call :build_go
call :build_cpp
echo === 所有版本构建完成 ===
goto menu

:exit
echo 退出构建脚本
exit /b 0
