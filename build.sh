#!/bin/bash

# PasteWith 构建脚本

set -e

echo "=== PasteWith 构建脚本 ==="

# 检测操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "检测到操作系统: ${MACHINE}"

# 函数：构建 Go 版本
build_go() {
    echo "=== 构建 Go 版本 ==="
    
    if ! command -v go &> /dev/null; then
        echo "错误: 未找到 Go 编译器"
        echo "请安装 Go: https://golang.org/dl/"
        return 1
    fi
    
    echo "Go 版本: $(go version)"
    
    echo "下载依赖..."
    go mod tidy
    
    echo "编译..."
    go build -o pastewith-go pastewith.go
    
    echo "Go 版本构建完成: pastewith-go"
}

# 函数：构建 C++ 版本
build_cpp() {
    echo "=== 构建 C++ 版本 ==="
    
    if ! command -v g++ &> /dev/null; then
        echo "错误: 未找到 g++ 编译器"
        echo "请安装 g++:"
        case "${MACHINE}" in
            Linux)
                echo "  Ubuntu/Debian: sudo apt-get install g++ libcurl4-openssl-dev libssl-dev libx11-dev"
                echo "  CentOS/RHEL: sudo dnf install gcc-c++ libcurl-devel openssl-devel libX11-devel"
                ;;
            Mac)
                echo "  macOS: brew install gcc curl openssl"
                ;;
        esac
        return 1
    fi
    
    echo "g++ 版本: $(g++ --version | head -n1)"
    
    echo "编译..."
    make clean 2>/dev/null || true
    make
    
    echo "C++ 版本构建完成: pastewith"
}

# 函数：检查 Python 依赖
check_python() {
    echo "=== 检查 Python 版本 ==="
    
    if ! command -v python3 &> /dev/null; then
        echo "错误: 未找到 Python3"
        echo "请安装 Python3: https://www.python.org/downloads/"
        return 1
    fi
    
    echo "Python 版本: $(python3 --version)"
    
    echo "检查 Python 依赖..."
    python3 -c "import pyperclip, PIL, requests" 2>/dev/null || {
        echo "缺少 Python 依赖，正在安装..."
        pip3 install pyperclip pillow requests
    }
    
    echo "Python 版本就绪: pastewith_pyperclip.py"
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择要构建的版本:"
    echo "1) Go 版本"
    echo "2) C++ 版本"
    echo "3) 检查 Python 版本"
    echo "4) 构建所有版本"
    echo "5) 退出"
    echo ""
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1)
            build_go
            ;;
        2)
            build_cpp
            ;;
        3)
            check_python
            ;;
        4)
            echo "=== 构建所有版本 ==="
            check_python
            build_go
            build_cpp
            echo "=== 所有版本构建完成 ==="
            ;;
        5)
            echo "退出构建脚本"
            exit 0
            ;;
        *)
            echo "无效选择，请输入 1-5"
            ;;
    esac
    
    echo ""
    read -p "按 Enter 键继续..."
done
