(['/home/<USER>/Dropbox/剪切板/pastewith_pyperclip.py'],
 ['/home/<USER>/Dropbox/剪切板'],
 [],
 [('/usr/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/usr/lib/python3.13/site-packages/_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.5 (main, Jun 11 2025, 22:06:31) [GCC]',
 [('pyi_rth_inspect',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/usr/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pastewith_pyperclip',
   '/home/<USER>/Dropbox/剪切板/pastewith_pyperclip.py',
   'PYSOURCE')],
 [('pkg_resources',
   '/usr/lib/python3.13/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   '/usr/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('subprocess', '/usr/lib64/python3.13/subprocess.py', 'PYMODULE'),
  ('selectors', '/usr/lib64/python3.13/selectors.py', 'PYMODULE'),
  ('contextlib', '/usr/lib64/python3.13/contextlib.py', 'PYMODULE'),
  ('threading', '/usr/lib64/python3.13/threading.py', 'PYMODULE'),
  ('_threading_local', '/usr/lib64/python3.13/_threading_local.py', 'PYMODULE'),
  ('signal', '/usr/lib64/python3.13/signal.py', 'PYMODULE'),
  ('struct', '/usr/lib64/python3.13/struct.py', 'PYMODULE'),
  ('packaging.metadata',
   '/usr/lib/python3.13/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('email.policy', '/usr/lib64/python3.13/email/policy.py', 'PYMODULE'),
  ('email', '/usr/lib64/python3.13/email/__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   '/usr/lib64/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/usr/lib64/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('base64', '/usr/lib64/python3.13/base64.py', 'PYMODULE'),
  ('getopt', '/usr/lib64/python3.13/getopt.py', 'PYMODULE'),
  ('gettext', '/usr/lib64/python3.13/gettext.py', 'PYMODULE'),
  ('copy', '/usr/lib64/python3.13/copy.py', 'PYMODULE'),
  ('string', '/usr/lib64/python3.13/string.py', 'PYMODULE'),
  ('urllib', '/usr/lib64/python3.13/urllib/__init__.py', 'PYMODULE'),
  ('email.charset', '/usr/lib64/python3.13/email/charset.py', 'PYMODULE'),
  ('email.encoders', '/usr/lib64/python3.13/email/encoders.py', 'PYMODULE'),
  ('quopri', '/usr/lib64/python3.13/quopri.py', 'PYMODULE'),
  ('email.quoprimime', '/usr/lib64/python3.13/email/quoprimime.py', 'PYMODULE'),
  ('email.base64mime', '/usr/lib64/python3.13/email/base64mime.py', 'PYMODULE'),
  ('email.errors', '/usr/lib64/python3.13/email/errors.py', 'PYMODULE'),
  ('email.contentmanager',
   '/usr/lib64/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/usr/lib64/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email.utils', '/usr/lib64/python3.13/email/utils.py', 'PYMODULE'),
  ('socket', '/usr/lib64/python3.13/socket.py', 'PYMODULE'),
  ('random', '/usr/lib64/python3.13/random.py', 'PYMODULE'),
  ('argparse', '/usr/lib64/python3.13/argparse.py', 'PYMODULE'),
  ('statistics', '/usr/lib64/python3.13/statistics.py', 'PYMODULE'),
  ('decimal', '/usr/lib64/python3.13/decimal.py', 'PYMODULE'),
  ('_pydecimal', '/usr/lib64/python3.13/_pydecimal.py', 'PYMODULE'),
  ('contextvars', '/usr/lib64/python3.13/contextvars.py', 'PYMODULE'),
  ('fractions', '/usr/lib64/python3.13/fractions.py', 'PYMODULE'),
  ('numbers', '/usr/lib64/python3.13/numbers.py', 'PYMODULE'),
  ('bisect', '/usr/lib64/python3.13/bisect.py', 'PYMODULE'),
  ('email._parseaddr', '/usr/lib64/python3.13/email/_parseaddr.py', 'PYMODULE'),
  ('calendar', '/usr/lib64/python3.13/calendar.py', 'PYMODULE'),
  ('datetime', '/usr/lib64/python3.13/datetime.py', 'PYMODULE'),
  ('_pydatetime', '/usr/lib64/python3.13/_pydatetime.py', 'PYMODULE'),
  ('_strptime', '/usr/lib64/python3.13/_strptime.py', 'PYMODULE'),
  ('email._policybase',
   '/usr/lib64/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.message', '/usr/lib64/python3.13/email/message.py', 'PYMODULE'),
  ('email.iterators', '/usr/lib64/python3.13/email/iterators.py', 'PYMODULE'),
  ('email.generator', '/usr/lib64/python3.13/email/generator.py', 'PYMODULE'),
  ('email.header', '/usr/lib64/python3.13/email/header.py', 'PYMODULE'),
  ('email.feedparser', '/usr/lib64/python3.13/email/feedparser.py', 'PYMODULE'),
  ('packaging.licenses._spdx',
   '/usr/lib/python3.13/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/usr/lib/python3.13/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/usr/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('dataclasses', '/usr/lib64/python3.13/dataclasses.py', 'PYMODULE'),
  ('packaging._structures',
   '/usr/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/usr/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('ast', '/usr/lib64/python3.13/ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   '/usr/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/usr/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('ctypes', '/usr/lib64/python3.13/ctypes/__init__.py', 'PYMODULE'),
  ('ctypes.util', '/usr/lib64/python3.13/ctypes/util.py', 'PYMODULE'),
  ('ctypes._aix', '/usr/lib64/python3.13/ctypes/_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/usr/lib64/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/usr/lib64/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/usr/lib64/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/usr/lib64/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes._endian', '/usr/lib64/python3.13/ctypes/_endian.py', 'PYMODULE'),
  ('packaging._elffile',
   '/usr/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging',
   '/usr/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('sysconfig', '/usr/lib64/python3.13/sysconfig/__init__.py', 'PYMODULE'),
  ('_sysconfigdata__linux_x86_64-linux-gnu',
   '/usr/lib64/python3.13/_sysconfigdata__linux_x86_64-linux-gnu.py',
   'PYMODULE'),
  ('_aix_support', '/usr/lib64/python3.13/_aix_support.py', 'PYMODULE'),
  ('importlib.util', '/usr/lib64/python3.13/importlib/util.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/lib64/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/lib64/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('csv', '/usr/lib64/python3.13/csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/lib64/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/lib64/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/lib64/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/lib64/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/lib64/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/lib64/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('json', '/usr/lib64/python3.13/json/__init__.py', 'PYMODULE'),
  ('json.encoder', '/usr/lib64/python3.13/json/encoder.py', 'PYMODULE'),
  ('json.decoder', '/usr/lib64/python3.13/json/decoder.py', 'PYMODULE'),
  ('json.scanner', '/usr/lib64/python3.13/json/scanner.py', 'PYMODULE'),
  ('importlib.readers',
   '/usr/lib64/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/lib64/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/lib64/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/lib64/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/lib64/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/usr/lib64/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/lib64/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/lib64/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tokenize', '/usr/lib64/python3.13/tokenize.py', 'PYMODULE'),
  ('token', '/usr/lib64/python3.13/token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/lib64/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', '/usr/lib64/python3.13/importlib/_abc.py', 'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   '/usr/lib/python3.13/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/usr/lib/python3.13/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('queue', '/usr/lib64/python3.13/queue.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('tarfile', '/usr/lib64/python3.13/tarfile.py', 'PYMODULE'),
  ('gzip', '/usr/lib64/python3.13/gzip.py', 'PYMODULE'),
  ('_compression', '/usr/lib64/python3.13/_compression.py', 'PYMODULE'),
  ('lzma', '/usr/lib64/python3.13/lzma.py', 'PYMODULE'),
  ('bz2', '/usr/lib64/python3.13/bz2.py', 'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('unittest.mock', '/usr/lib64/python3.13/unittest/mock.py', 'PYMODULE'),
  ('unittest', '/usr/lib64/python3.13/unittest/__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   '/usr/lib64/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals', '/usr/lib64/python3.13/unittest/signals.py', 'PYMODULE'),
  ('unittest.main', '/usr/lib64/python3.13/unittest/main.py', 'PYMODULE'),
  ('unittest.runner', '/usr/lib64/python3.13/unittest/runner.py', 'PYMODULE'),
  ('unittest.loader', '/usr/lib64/python3.13/unittest/loader.py', 'PYMODULE'),
  ('fnmatch', '/usr/lib64/python3.13/fnmatch.py', 'PYMODULE'),
  ('unittest.suite', '/usr/lib64/python3.13/unittest/suite.py', 'PYMODULE'),
  ('unittest.case', '/usr/lib64/python3.13/unittest/case.py', 'PYMODULE'),
  ('unittest._log', '/usr/lib64/python3.13/unittest/_log.py', 'PYMODULE'),
  ('difflib', '/usr/lib64/python3.13/difflib.py', 'PYMODULE'),
  ('unittest.result', '/usr/lib64/python3.13/unittest/result.py', 'PYMODULE'),
  ('unittest.util', '/usr/lib64/python3.13/unittest/util.py', 'PYMODULE'),
  ('pprint', '/usr/lib64/python3.13/pprint.py', 'PYMODULE'),
  ('asyncio', '/usr/lib64/python3.13/asyncio/__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   '/usr/lib64/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log', '/usr/lib64/python3.13/asyncio/log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   '/usr/lib64/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/usr/lib64/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/usr/lib64/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('ssl', '/usr/lib64/python3.13/ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   '/usr/lib64/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/usr/lib64/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', '/usr/lib64/python3.13/asyncio/threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   '/usr/lib64/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/usr/lib64/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', '/usr/lib64/python3.13/asyncio/streams.py', 'PYMODULE'),
  ('asyncio.runners', '/usr/lib64/python3.13/asyncio/runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   '/usr/lib64/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', '/usr/lib64/python3.13/asyncio/trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   '/usr/lib64/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', '/usr/lib64/python3.13/asyncio/timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', '/usr/lib64/python3.13/asyncio/tasks.py', 'PYMODULE'),
  ('asyncio.queues', '/usr/lib64/python3.13/asyncio/queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   '/usr/lib64/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', '/usr/lib64/python3.13/asyncio/locks.py', 'PYMODULE'),
  ('asyncio.mixins', '/usr/lib64/python3.13/asyncio/mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', '/usr/lib64/python3.13/asyncio/sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   '/usr/lib64/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/usr/lib64/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures', '/usr/lib64/python3.13/asyncio/futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   '/usr/lib64/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/usr/lib64/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events', '/usr/lib64/python3.13/asyncio/events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   '/usr/lib64/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/usr/lib64/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/usr/lib64/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('concurrent.futures',
   '/usr/lib64/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/lib64/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/lib64/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/lib64/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/lib64/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/lib64/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/usr/lib64/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy', '/usr/lib64/python3.13/runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   '/usr/lib64/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/lib64/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/lib64/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/lib64/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/lib64/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/lib64/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/lib64/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/lib64/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/lib64/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/lib64/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/lib64/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/lib64/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/lib64/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/lib64/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets', '/usr/lib64/python3.13/secrets.py', 'PYMODULE'),
  ('hmac', '/usr/lib64/python3.13/hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/lib64/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/lib64/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('pickle', '/usr/lib64/python3.13/pickle.py', 'PYMODULE'),
  ('_compat_pickle', '/usr/lib64/python3.13/_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/lib64/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/lib64/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client', '/usr/lib64/python3.13/xmlrpc/client.py', 'PYMODULE'),
  ('xmlrpc', '/usr/lib64/python3.13/xmlrpc/__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   '/usr/lib64/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers', '/usr/lib64/python3.13/xml/parsers/__init__.py', 'PYMODULE'),
  ('xml', '/usr/lib64/python3.13/xml/__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/lib64/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', '/usr/lib64/python3.13/xml/sax/saxutils.py', 'PYMODULE'),
  ('urllib.request', '/usr/lib64/python3.13/urllib/request.py', 'PYMODULE'),
  ('ipaddress', '/usr/lib64/python3.13/ipaddress.py', 'PYMODULE'),
  ('getpass', '/usr/lib64/python3.13/getpass.py', 'PYMODULE'),
  ('nturl2path', '/usr/lib64/python3.13/nturl2path.py', 'PYMODULE'),
  ('ftplib', '/usr/lib64/python3.13/ftplib.py', 'PYMODULE'),
  ('netrc', '/usr/lib64/python3.13/netrc.py', 'PYMODULE'),
  ('mimetypes', '/usr/lib64/python3.13/mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', '/usr/lib64/python3.13/http/cookiejar.py', 'PYMODULE'),
  ('http', '/usr/lib64/python3.13/http/__init__.py', 'PYMODULE'),
  ('urllib.response', '/usr/lib64/python3.13/urllib/response.py', 'PYMODULE'),
  ('urllib.error', '/usr/lib64/python3.13/urllib/error.py', 'PYMODULE'),
  ('xml.sax', '/usr/lib64/python3.13/xml/sax/__init__.py', 'PYMODULE'),
  ('xml.sax.handler', '/usr/lib64/python3.13/xml/sax/handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/lib64/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/usr/lib64/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client', '/usr/lib64/python3.13/http/client.py', 'PYMODULE'),
  ('multiprocessing',
   '/usr/lib64/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/lib64/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent', '/usr/lib64/python3.13/concurrent/__init__.py', 'PYMODULE'),
  ('setuptools._distutils.util',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('py_compile', '/usr/lib64/python3.13/py_compile.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('site', '/usr/lib64/python3.13/site.py', 'PYMODULE'),
  ('_pyrepl.main', '/usr/lib64/python3.13/_pyrepl/main.py', 'PYMODULE'),
  ('_pyrepl', '/usr/lib64/python3.13/_pyrepl/__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', '/usr/lib64/python3.13/_pyrepl/curses.py', 'PYMODULE'),
  ('curses', '/usr/lib64/python3.13/curses/__init__.py', 'PYMODULE'),
  ('curses.has_key', '/usr/lib64/python3.13/curses/has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   '/usr/lib64/python3.13/_pyrepl/_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', '/usr/lib64/python3.13/_pyrepl/input.py', 'PYMODULE'),
  ('_pyrepl.keymap', '/usr/lib64/python3.13/_pyrepl/keymap.py', 'PYMODULE'),
  ('_pyrepl.types', '/usr/lib64/python3.13/_pyrepl/types.py', 'PYMODULE'),
  ('_pyrepl.commands', '/usr/lib64/python3.13/_pyrepl/commands.py', 'PYMODULE'),
  ('_pyrepl.pager', '/usr/lib64/python3.13/_pyrepl/pager.py', 'PYMODULE'),
  ('tty', '/usr/lib64/python3.13/tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   '/usr/lib64/python3.13/_pyrepl/historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', '/usr/lib64/python3.13/_pyrepl/reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   '/usr/lib64/python3.13/_pyrepl/_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', '/usr/lib64/python3.13/_pyrepl/utils.py', 'PYMODULE'),
  ('_colorize', '/usr/lib64/python3.13/_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', '/usr/lib64/python3.13/_pyrepl/console.py', 'PYMODULE'),
  ('code', '/usr/lib64/python3.13/code.py', 'PYMODULE'),
  ('codeop', '/usr/lib64/python3.13/codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', '/usr/lib64/python3.13/_pyrepl/trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   '/usr/lib64/python3.13/_pyrepl/simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   '/usr/lib64/python3.13/_pyrepl/unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   '/usr/lib64/python3.13/_pyrepl/unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   '/usr/lib64/python3.13/_pyrepl/base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   '/usr/lib64/python3.13/_pyrepl/fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   '/usr/lib64/python3.13/_pyrepl/windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   '/usr/lib64/python3.13/_pyrepl/windows_eventqueue.py',
   'PYMODULE'),
  ('ctypes.wintypes', '/usr/lib64/python3.13/ctypes/wintypes.py', 'PYMODULE'),
  ('_pyrepl.readline', '/usr/lib64/python3.13/_pyrepl/readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   '/usr/lib64/python3.13/_pyrepl/completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', '/usr/lib64/python3.13/rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', '/usr/lib64/python3.13/_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', '/usr/lib64/python3.13/pydoc.py', 'PYMODULE'),
  ('webbrowser', '/usr/lib64/python3.13/webbrowser.py', 'PYMODULE'),
  ('_ios_support', '/usr/lib64/python3.13/_ios_support.py', 'PYMODULE'),
  ('shlex', '/usr/lib64/python3.13/shlex.py', 'PYMODULE'),
  ('http.server', '/usr/lib64/python3.13/http/server.py', 'PYMODULE'),
  ('socketserver', '/usr/lib64/python3.13/socketserver.py', 'PYMODULE'),
  ('html', '/usr/lib64/python3.13/html/__init__.py', 'PYMODULE'),
  ('html.entities', '/usr/lib64/python3.13/html/entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   '/usr/lib64/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data', '/usr/lib64/python3.13/pydoc_data/__init__.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('configparser', '/usr/lib64/python3.13/configparser.py', 'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/usr/lib/python3.13/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/usr/lib/python3.13/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/usr/lib/python3.13/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/usr/lib/python3.13/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/usr/lib/python3.13/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/usr/lib/python3.13/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/usr/lib/python3.13/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/usr/lib/python3.13/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/usr/lib/python3.13/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/usr/lib/python3.13/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/usr/lib/python3.13/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/usr/lib/python3.13/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/usr/lib/python3.13/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/usr/lib/python3.13/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/usr/lib/python3.13/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/usr/lib/python3.13/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/usr/lib/python3.13/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/usr/lib/python3.13/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/usr/lib/python3.13/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/usr/lib/python3.13/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/usr/lib/python3.13/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/usr/lib/python3.13/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/usr/lib/python3.13/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/usr/lib/python3.13/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/usr/lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/usr/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/usr/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/usr/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/usr/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/usr/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/usr/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/usr/lib/python3.13/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib', '/usr/lib64/python3.13/tomllib/__init__.py', 'PYMODULE'),
  ('tomllib._parser', '/usr/lib64/python3.13/tomllib/_parser.py', 'PYMODULE'),
  ('tomllib._types', '/usr/lib64/python3.13/tomllib/_types.py', 'PYMODULE'),
  ('tomllib._re', '/usr/lib64/python3.13/tomllib/_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/usr/lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/usr/lib/python3.13/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/usr/lib/python3.13/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('glob', '/usr/lib64/python3.13/glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   '/usr/lib/python3.13/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/usr/lib/python3.13/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   '/usr/lib/python3.13/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/usr/lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/usr/lib/python3.13/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/usr/lib/python3.13/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/usr/lib/python3.13/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/usr/lib/python3.13/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('dis', '/usr/lib64/python3.13/dis.py', 'PYMODULE'),
  ('opcode', '/usr/lib64/python3.13/opcode.py', 'PYMODULE'),
  ('_opcode_metadata', '/usr/lib64/python3.13/_opcode_metadata.py', 'PYMODULE'),
  ('setuptools._imp',
   '/usr/lib/python3.13/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/usr/lib/python3.13/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/usr/lib/python3.13/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/usr/lib/python3.13/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/usr/lib/python3.13/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/usr/lib/python3.13/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/usr/lib/python3.13/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/usr/lib/python3.13/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('packaging.version',
   '/usr/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging.utils',
   '/usr/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/usr/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/usr/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   '/usr/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('typing', '/usr/lib64/python3.13/typing.py', 'PYMODULE'),
  ('zipimport', '/usr/lib64/python3.13/zipimport.py', 'PYMODULE'),
  ('zipfile', '/usr/lib64/python3.13/zipfile/__init__.py', 'PYMODULE'),
  ('zipfile._path',
   '/usr/lib64/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/lib64/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('textwrap', '/usr/lib64/python3.13/textwrap.py', 'PYMODULE'),
  ('plistlib', '/usr/lib64/python3.13/plistlib.py', 'PYMODULE'),
  ('platform', '/usr/lib64/python3.13/platform.py', 'PYMODULE'),
  ('pkgutil', '/usr/lib64/python3.13/pkgutil.py', 'PYMODULE'),
  ('inspect', '/usr/lib64/python3.13/inspect.py', 'PYMODULE'),
  ('importlib.machinery',
   '/usr/lib64/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.abc', '/usr/lib64/python3.13/importlib/abc.py', 'PYMODULE'),
  ('importlib', '/usr/lib64/python3.13/importlib/__init__.py', 'PYMODULE'),
  ('email.parser', '/usr/lib64/python3.13/email/parser.py', 'PYMODULE'),
  ('__future__', '/usr/lib64/python3.13/__future__.py', 'PYMODULE'),
  ('_py_abc', '/usr/lib64/python3.13/_py_abc.py', 'PYMODULE'),
  ('tracemalloc', '/usr/lib64/python3.13/tracemalloc.py', 'PYMODULE'),
  ('stringprep', '/usr/lib64/python3.13/stringprep.py', 'PYMODULE'),
  ('shutil', '/usr/lib64/python3.13/shutil.py', 'PYMODULE'),
  ('logging', '/usr/lib64/python3.13/logging/__init__.py', 'PYMODULE'),
  ('PIL.ImageGrab',
   '/usr/lib64/python3.13/site-packages/PIL/ImageGrab.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   '/usr/lib64/python3.13/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/usr/lib64/python3.13/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/usr/lib64/python3.13/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/usr/lib64/python3.13/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('colorsys', '/usr/lib64/python3.13/colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/usr/lib64/python3.13/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/usr/lib64/python3.13/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/usr/lib64/python3.13/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/usr/lib64/python3.13/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/usr/lib64/python3.13/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL._typing',
   '/usr/lib64/python3.13/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('PIL._util', '/usr/lib64/python3.13/site-packages/PIL/_util.py', 'PYMODULE'),
  ('PIL._deprecate',
   '/usr/lib64/python3.13/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/usr/lib64/python3.13/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/usr/lib64/python3.13/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/usr/lib64/python3.13/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/usr/lib64/python3.13/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.Image', '/usr/lib64/python3.13/site-packages/PIL/Image.py', 'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   '/usr/lib64/python3.13/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/usr/lib64/python3.13/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('olefile',
   '/usr/lib/python3.13/site-packages/olefile/__init__.py',
   'PYMODULE'),
  ('olefile.olefile',
   '/usr/lib/python3.13/site-packages/olefile/olefile.py',
   'PYMODULE'),
  ('optparse', '/usr/lib64/python3.13/optparse.py', 'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/usr/lib64/python3.13/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/usr/lib64/python3.13/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/usr/lib64/python3.13/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/usr/lib64/python3.13/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/usr/lib64/python3.13/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/usr/lib64/python3.13/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/usr/lib64/python3.13/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/usr/lib64/python3.13/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/usr/lib64/python3.13/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/usr/lib64/python3.13/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/usr/lib64/python3.13/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree', '/usr/lib64/python3.13/xml/etree/__init__.py', 'PYMODULE'),
  ('PIL.ImageMode',
   '/usr/lib64/python3.13/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL', '/usr/lib64/python3.13/site-packages/PIL/__init__.py', 'PYMODULE'),
  ('PIL._version',
   '/usr/lib64/python3.13/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('pyperclip',
   '/usr/lib/python3.13/site-packages/pyperclip/__init__.py',
   'PYMODULE'),
  ('requests',
   '/usr/lib/python3.13/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/usr/lib/python3.13/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/usr/lib/python3.13/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.compat',
   '/usr/lib/python3.13/site-packages/requests/compat.py',
   'PYMODULE'),
  ('http.cookies', '/usr/lib64/python3.13/http/cookies.py', 'PYMODULE'),
  ('requests.models',
   '/usr/lib/python3.13/site-packages/requests/models.py',
   'PYMODULE'),
  ('idna', '/usr/lib/python3.13/site-packages/idna/__init__.py', 'PYMODULE'),
  ('idna.package_data',
   '/usr/lib/python3.13/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   '/usr/lib/python3.13/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.core', '/usr/lib/python3.13/site-packages/idna/core.py', 'PYMODULE'),
  ('idna.uts46data',
   '/usr/lib/python3.13/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/usr/lib/python3.13/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   '/usr/lib/python3.13/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   '/usr/lib/python3.13/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.auth',
   '/usr/lib/python3.13/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/usr/lib/python3.13/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   '/usr/lib/python3.13/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/usr/lib/python3.13/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/usr/lib/python3.13/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/usr/lib/python3.13/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/usr/lib/python3.13/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/usr/lib/python3.13/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/usr/lib/python3.13/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/usr/lib/python3.13/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.response',
   '/usr/lib/python3.13/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('zstandard',
   '/usr/lib64/python3.13/site-packages/zstandard/__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   '/usr/lib64/python3.13/site-packages/zstandard/backend_cffi.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/usr/lib/python3.13/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/usr/lib/python3.13/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   '/usr/lib/python3.13/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/usr/lib/python3.13/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/usr/lib/python3.13/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/usr/lib/python3.13/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('h2.events', '/usr/lib/python3.13/site-packages/h2/events.py', 'PYMODULE'),
  ('h2', '/usr/lib/python3.13/site-packages/h2/__init__.py', 'PYMODULE'),
  ('h2.errors', '/usr/lib/python3.13/site-packages/h2/errors.py', 'PYMODULE'),
  ('hyperframe.frame',
   '/usr/lib/python3.13/site-packages/hyperframe/frame.py',
   'PYMODULE'),
  ('hyperframe',
   '/usr/lib/python3.13/site-packages/hyperframe/__init__.py',
   'PYMODULE'),
  ('hyperframe.flags',
   '/usr/lib/python3.13/site-packages/hyperframe/flags.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   '/usr/lib/python3.13/site-packages/hyperframe/exceptions.py',
   'PYMODULE'),
  ('hpack', '/usr/lib/python3.13/site-packages/hpack/__init__.py', 'PYMODULE'),
  ('hpack.struct',
   '/usr/lib/python3.13/site-packages/hpack/struct.py',
   'PYMODULE'),
  ('hpack.hpack',
   '/usr/lib/python3.13/site-packages/hpack/hpack.py',
   'PYMODULE'),
  ('hpack.table',
   '/usr/lib/python3.13/site-packages/hpack/table.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   '/usr/lib/python3.13/site-packages/hpack/huffman_table.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   '/usr/lib/python3.13/site-packages/hpack/huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman',
   '/usr/lib/python3.13/site-packages/hpack/huffman.py',
   'PYMODULE'),
  ('hpack.exceptions',
   '/usr/lib/python3.13/site-packages/hpack/exceptions.py',
   'PYMODULE'),
  ('h2.settings',
   '/usr/lib/python3.13/site-packages/h2/settings.py',
   'PYMODULE'),
  ('h2.exceptions',
   '/usr/lib/python3.13/site-packages/h2/exceptions.py',
   'PYMODULE'),
  ('h2.connection',
   '/usr/lib/python3.13/site-packages/h2/connection.py',
   'PYMODULE'),
  ('h2.windows', '/usr/lib/python3.13/site-packages/h2/windows.py', 'PYMODULE'),
  ('h2.utilities',
   '/usr/lib/python3.13/site-packages/h2/utilities.py',
   'PYMODULE'),
  ('h2.stream', '/usr/lib/python3.13/site-packages/h2/stream.py', 'PYMODULE'),
  ('h2.frame_buffer',
   '/usr/lib/python3.13/site-packages/h2/frame_buffer.py',
   'PYMODULE'),
  ('h2.config', '/usr/lib/python3.13/site-packages/h2/config.py', 'PYMODULE'),
  ('urllib3._collections',
   '/usr/lib/python3.13/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('brotli', '/usr/lib64/python3.13/site-packages/brotli.py', 'PYMODULE'),
  ('urllib3._base_connection',
   '/usr/lib/python3.13/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/usr/lib/python3.13/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/usr/lib/python3.13/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/usr/lib/python3.13/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/usr/lib/python3.13/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/usr/lib/python3.13/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/usr/lib/python3.13/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/usr/lib/python3.13/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/usr/lib/python3.13/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('requests.api',
   '/usr/lib/python3.13/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.sessions',
   '/usr/lib/python3.13/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   '/usr/lib/python3.13/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('socks', '/usr/lib/python3.13/site-packages/socks.py', 'PYMODULE'),
  ('urllib3.poolmanager',
   '/usr/lib/python3.13/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   '/usr/lib/python3.13/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests.utils',
   '/usr/lib/python3.13/site-packages/requests/utils.py',
   'PYMODULE'),
  ('requests.certs',
   '/usr/lib/python3.13/site-packages/requests/certs.py',
   'PYMODULE'),
  ('certifi',
   '/usr/lib/python3.13/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/usr/lib/python3.13/site-packages/certifi/core.py',
   'PYMODULE'),
  ('requests.packages',
   '/usr/lib/python3.13/site-packages/requests/packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/usr/lib/python3.13/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('cryptography',
   '/usr/lib64/python3.13/site-packages/cryptography/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/bindings/openssl/binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/bindings/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   '/usr/lib64/python3.13/site-packages/cryptography/exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/bindings/openssl/_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/bindings/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/backends/openssl/backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/decrepit/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/decrepit/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/decrepit/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/serialization/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/serialization/ssh.py',
   'PYMODULE'),
  ('bcrypt',
   '/usr/lib64/python3.13/site-packages/bcrypt/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/serialization/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/backends/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/backends/__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   '/usr/lib64/python3.13/site-packages/cryptography/x509/certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   '/usr/lib64/python3.13/site-packages/cryptography/__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   '/usr/lib64/python3.13/site-packages/cryptography/utils.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   '/usr/lib/python3.13/site-packages/OpenSSL/crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   '/usr/lib/python3.13/site-packages/OpenSSL/__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   '/usr/lib/python3.13/site-packages/OpenSSL/version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   '/usr/lib/python3.13/site-packages/OpenSSL/_util.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   '/usr/lib/python3.13/site-packages/OpenSSL/SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('chardet',
   '/usr/lib/python3.13/site-packages/chardet/__init__.py',
   'PYMODULE'),
  ('chardet.version',
   '/usr/lib/python3.13/site-packages/chardet/version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   '/usr/lib/python3.13/site-packages/chardet/universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   '/usr/lib/python3.13/site-packages/chardet/utf1632prober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   '/usr/lib/python3.13/site-packages/chardet/sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   '/usr/lib/python3.13/site-packages/chardet/sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   '/usr/lib/python3.13/site-packages/chardet/langturkishmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   '/usr/lib/python3.13/site-packages/chardet/langthaimodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   '/usr/lib/python3.13/site-packages/chardet/langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   '/usr/lib/python3.13/site-packages/chardet/langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   '/usr/lib/python3.13/site-packages/chardet/langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   '/usr/lib/python3.13/site-packages/chardet/langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   '/usr/lib/python3.13/site-packages/chardet/hebrewprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   '/usr/lib/python3.13/site-packages/chardet/mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   '/usr/lib/python3.13/site-packages/chardet/utf8prober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   '/usr/lib/python3.13/site-packages/chardet/mbcssm.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   '/usr/lib/python3.13/site-packages/chardet/codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   '/usr/lib/python3.13/site-packages/chardet/codingstatemachine.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   '/usr/lib/python3.13/site-packages/chardet/sjisprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   '/usr/lib/python3.13/site-packages/chardet/mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   '/usr/lib/python3.13/site-packages/chardet/jpcntx.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   '/usr/lib/python3.13/site-packages/chardet/chardistribution.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   '/usr/lib/python3.13/site-packages/chardet/johabfreq.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   '/usr/lib/python3.13/site-packages/chardet/jisfreq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   '/usr/lib/python3.13/site-packages/chardet/gb2312freq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   '/usr/lib/python3.13/site-packages/chardet/euctwfreq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   '/usr/lib/python3.13/site-packages/chardet/euckrfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   '/usr/lib/python3.13/site-packages/chardet/big5freq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   '/usr/lib/python3.13/site-packages/chardet/johabprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   '/usr/lib/python3.13/site-packages/chardet/gb2312prober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   '/usr/lib/python3.13/site-packages/chardet/euctwprober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   '/usr/lib/python3.13/site-packages/chardet/euckrprober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   '/usr/lib/python3.13/site-packages/chardet/eucjpprober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   '/usr/lib/python3.13/site-packages/chardet/cp949prober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   '/usr/lib/python3.13/site-packages/chardet/big5prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   '/usr/lib/python3.13/site-packages/chardet/macromanprober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   '/usr/lib/python3.13/site-packages/chardet/latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   '/usr/lib/python3.13/site-packages/chardet/escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   '/usr/lib/python3.13/site-packages/chardet/escsm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   '/usr/lib/python3.13/site-packages/chardet/resultdict.py',
   'PYMODULE'),
  ('chardet.enums',
   '/usr/lib/python3.13/site-packages/chardet/enums.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   '/usr/lib/python3.13/site-packages/chardet/charsetprober.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   '/usr/lib/python3.13/site-packages/chardet/charsetgroupprober.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/usr/lib/python3.13/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/usr/lib/python3.13/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/usr/lib/python3.13/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/usr/lib/python3.13/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/usr/lib/python3.13/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/usr/lib/python3.13/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   '/usr/lib/python3.13/site-packages/charset_normalizer/md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/usr/lib/python3.13/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/usr/lib/python3.13/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/usr/lib/python3.13/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('urllib3',
   '/usr/lib/python3.13/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/usr/lib/python3.13/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('pathlib', '/usr/lib64/python3.13/pathlib/__init__.py', 'PYMODULE'),
  ('pathlib._local', '/usr/lib64/python3.13/pathlib/_local.py', 'PYMODULE'),
  ('pathlib._abc', '/usr/lib64/python3.13/pathlib/_abc.py', 'PYMODULE'),
  ('urllib.parse', '/usr/lib64/python3.13/urllib/parse.py', 'PYMODULE'),
  ('tempfile', '/usr/lib64/python3.13/tempfile.py', 'PYMODULE'),
  ('hashlib', '/usr/lib64/python3.13/hashlib.py', 'PYMODULE')],
 [('libpython3.13.so.1.0',
   '/lib64/glibc-hwcaps/x86-64-v3/libpython3.13.so.1.0',
   'BINARY'),
  ('ossl-modules/legacy.so',
   '/lib64/glibc-hwcaps/x86-64-v3/ossl-modules/legacy.so',
   'BINARY'),
  ('libobjc.so.4', '/lib64/libobjc.so.4', 'BINARY'),
  ('lib-dynload/grp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/grp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/math.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/select.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_posixsubprocess.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/fcntl.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_struct.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/binascii.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/array.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_socket.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_statistics.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_contextvars.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_decimal.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_random.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_bisect.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_datetime.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_ctypes.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_csv.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_json.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/resource.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/syslog.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_queue.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_lzma.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_bz2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/zlib.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_ssl.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_asyncio.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/mmap.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_posixshmem.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_hashlib.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_pickle.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_multiprocessing.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/pyexpat.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/termios.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_curses.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/unicodedata.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/readline.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_opcode.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_heapq.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_multibytecodec.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_jp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_kr.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_iso2022.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_cn.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_tw.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_hk.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_imaging.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_webp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_imagingcms.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_imagingmath.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_elementtree.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('zstandard/_cffi.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/zstandard/_cffi.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('zstandard/backend_c.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/zstandard/backend_c.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('_brotli.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/_brotli.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/_cffi_backend.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('cryptography/hazmat/bindings/_rust.abi3.so',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/bindings/_rust.abi3.so',
   'EXTENSION'),
  ('bcrypt/_bcrypt.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/bcrypt/_bcrypt.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha3.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_blake2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_md5.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha1.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libnxegl.so', '/usr/NX/lib/libnxegl.so', 'BINARY'),
  ('libcrypto.so.3.5.1',
   '/lib64/glibc-hwcaps/x86-64-v3/libcrypto.so.3.5.1',
   'BINARY'),
  ('libjitterentropy.so.3', '/lib64/libjitterentropy.so.3', 'BINARY'),
  ('libz.so.1.3.1', '/lib64/glibc-hwcaps/x86-64-v3/libz.so.1.3.1', 'BINARY'),
  ('libgcc_s.so.1', '/lib64/libgcc_s.so.1', 'BINARY'),
  ('libmpdec.so.4', '/lib64/libmpdec.so.4', 'BINARY'),
  ('libffi.so.8', '/lib64/libffi.so.8', 'BINARY'),
  ('liblzma.so.5.8.1',
   '/lib64/glibc-hwcaps/x86-64-v3/liblzma.so.5.8.1',
   'BINARY'),
  ('libbz2.so.1.0.6',
   '/lib64/glibc-hwcaps/x86-64-v3/libbz2.so.1.0.6',
   'BINARY'),
  ('libssl.so.3.5.1',
   '/lib64/glibc-hwcaps/x86-64-v3/libssl.so.3.5.1',
   'BINARY'),
  ('libexpat.so.1', '/lib64/libexpat.so.1', 'BINARY'),
  ('libtinfo.so.6', '/lib64/libtinfo.so.6', 'BINARY'),
  ('libncursesw.so.6', '/lib64/libncursesw.so.6', 'BINARY'),
  ('libreadline.so.8', '/lib64/libreadline.so.8', 'BINARY'),
  ('libimagequant.so.0', '/lib64/libimagequant.so.0', 'BINARY'),
  ('libtiff.so.6', '/lib64/libtiff.so.6', 'BINARY'),
  ('libXau.so.6', '/lib64/libXau.so.6', 'BINARY'),
  ('libzstd.so.1.5.7',
   '/lib64/glibc-hwcaps/x86-64-v3/libzstd.so.1.5.7',
   'BINARY'),
  ('libjpeg.so.8.3.2',
   '/lib64/glibc-hwcaps/x86-64-v3/libjpeg.so.8.3.2',
   'BINARY'),
  ('libgomp.so.1', '/lib64/libgomp.so.1', 'BINARY'),
  ('libopenjp2.so.2.5.3',
   '/lib64/glibc-hwcaps/x86-64-v3/libopenjp2.so.2.5.3',
   'BINARY'),
  ('libjbig.so.2', '/lib64/libjbig.so.2', 'BINARY'),
  ('libwebpdemux.so.2', '/lib64/libwebpdemux.so.2', 'BINARY'),
  ('libsharpyuv.so.0', '/usr/lib64/libsharpyuv.so.0', 'BINARY'),
  ('libwebp.so.7', '/lib64/libwebp.so.7', 'BINARY'),
  ('libwebpmux.so.3', '/lib64/libwebpmux.so.3', 'BINARY'),
  ('liblcms2.so.2', '/lib64/liblcms2.so.2', 'BINARY')],
 [],
 [],
 [('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('certifi/py.typed',
   '/usr/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('cryptography-44.0.3.dist-info/RECORD',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/RECORD',
   'DATA'),
  ('cryptography-44.0.3.dist-info/INSTALLER',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/INSTALLER',
   'DATA'),
  ('cryptography-44.0.3.dist-info/licenses/LICENSE',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/licenses/LICENSE',
   'DATA'),
  ('cryptography-44.0.3.dist-info/REQUESTED',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/REQUESTED',
   'DATA'),
  ('cryptography-44.0.3.dist-info/METADATA',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/METADATA',
   'DATA'),
  ('cryptography-44.0.3.dist-info/licenses/LICENSE.BSD',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/licenses/LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.3.dist-info/WHEEL',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/WHEEL',
   'DATA'),
  ('cryptography-44.0.3.dist-info/licenses/LICENSE.APACHE',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/licenses/LICENSE.APACHE',
   'DATA'),
  ('h2-4.2.0.dist-info/top_level.txt',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/top_level.txt',
   'DATA'),
  ('h2-4.2.0.dist-info/METADATA',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/METADATA',
   'DATA'),
  ('h2-4.2.0.dist-info/REQUESTED',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/REQUESTED',
   'DATA'),
  ('h2-4.2.0.dist-info/LICENSE',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED',
   'DATA'),
  ('h2-4.2.0.dist-info/INSTALLER',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/INSTALLER',
   'DATA'),
  ('h2-4.2.0.dist-info/RECORD',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/METADATA',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/RECORD',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD',
   'DATA'),
  ('h2-4.2.0.dist-info/WHEEL',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL',
   'DATA'),
  ('base_library.zip',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/base_library.zip',
   'DATA')])
