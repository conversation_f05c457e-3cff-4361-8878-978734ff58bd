('/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/pastewith_pyperclip.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_struct.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/zlib.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('struct',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/usr/lib64/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/usr/lib64/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/usr/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pastewith_pyperclip',
   '/home/<USER>/Dropbox/剪切板/pastewith_pyperclip.py',
   'PYSOURCE'),
  ('libpython3.13.so.1.0',
   '/lib64/glibc-hwcaps/x86-64-v3/libpython3.13.so.1.0',
   'BINARY'),
  ('ossl-modules/legacy.so',
   '/lib64/glibc-hwcaps/x86-64-v3/ossl-modules/legacy.so',
   'BINARY'),
  ('libobjc.so.4', '/lib64/libobjc.so.4', 'BINARY'),
  ('lib-dynload/grp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/grp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/math.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/select.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_posixsubprocess.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/fcntl.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/binascii.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/array.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_socket.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_statistics.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_contextvars.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_decimal.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_random.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_bisect.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_datetime.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_ctypes.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_csv.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_json.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/resource.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/syslog.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_queue.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_lzma.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_bz2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_ssl.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_asyncio.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/mmap.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_posixshmem.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_hashlib.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_pickle.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_multiprocessing.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/pyexpat.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/termios.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_curses.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/unicodedata.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/readline.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_opcode.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_heapq.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_multibytecodec.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_jp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_kr.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_iso2022.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_cn.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_tw.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_hk.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_imaging.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_webp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_imagingcms.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/PIL/_imagingmath.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_elementtree.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('zstandard/_cffi.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/zstandard/_cffi.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('zstandard/backend_c.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/zstandard/backend_c.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('_brotli.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/_brotli.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/_cffi_backend.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('cryptography/hazmat/bindings/_rust.abi3.so',
   '/usr/lib64/python3.13/site-packages/cryptography/hazmat/bindings/_rust.abi3.so',
   'EXTENSION'),
  ('bcrypt/_bcrypt.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/site-packages/bcrypt/_bcrypt.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha3.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_blake2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_md5.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha1.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libnxegl.so', '/usr/NX/lib/libnxegl.so', 'BINARY'),
  ('libcrypto.so.3.5.1',
   '/lib64/glibc-hwcaps/x86-64-v3/libcrypto.so.3.5.1',
   'BINARY'),
  ('libjitterentropy.so.3', '/lib64/libjitterentropy.so.3', 'BINARY'),
  ('libz.so.1.3.1', '/lib64/glibc-hwcaps/x86-64-v3/libz.so.1.3.1', 'BINARY'),
  ('libgcc_s.so.1', '/lib64/libgcc_s.so.1', 'BINARY'),
  ('libmpdec.so.4', '/lib64/libmpdec.so.4', 'BINARY'),
  ('libffi.so.8', '/lib64/libffi.so.8', 'BINARY'),
  ('liblzma.so.5.8.1',
   '/lib64/glibc-hwcaps/x86-64-v3/liblzma.so.5.8.1',
   'BINARY'),
  ('libbz2.so.1.0.6',
   '/lib64/glibc-hwcaps/x86-64-v3/libbz2.so.1.0.6',
   'BINARY'),
  ('libssl.so.3.5.1',
   '/lib64/glibc-hwcaps/x86-64-v3/libssl.so.3.5.1',
   'BINARY'),
  ('libexpat.so.1', '/lib64/libexpat.so.1', 'BINARY'),
  ('libtinfo.so.6', '/lib64/libtinfo.so.6', 'BINARY'),
  ('libncursesw.so.6', '/lib64/libncursesw.so.6', 'BINARY'),
  ('libreadline.so.8', '/lib64/libreadline.so.8', 'BINARY'),
  ('libimagequant.so.0', '/lib64/libimagequant.so.0', 'BINARY'),
  ('libtiff.so.6', '/lib64/libtiff.so.6', 'BINARY'),
  ('libXau.so.6', '/lib64/libXau.so.6', 'BINARY'),
  ('libzstd.so.1.5.7',
   '/lib64/glibc-hwcaps/x86-64-v3/libzstd.so.1.5.7',
   'BINARY'),
  ('libjpeg.so.8.3.2',
   '/lib64/glibc-hwcaps/x86-64-v3/libjpeg.so.8.3.2',
   'BINARY'),
  ('libgomp.so.1', '/lib64/libgomp.so.1', 'BINARY'),
  ('libopenjp2.so.2.5.3',
   '/lib64/glibc-hwcaps/x86-64-v3/libopenjp2.so.2.5.3',
   'BINARY'),
  ('libjbig.so.2', '/lib64/libjbig.so.2', 'BINARY'),
  ('libwebpdemux.so.2', '/lib64/libwebpdemux.so.2', 'BINARY'),
  ('libsharpyuv.so.0', '/usr/lib64/libsharpyuv.so.0', 'BINARY'),
  ('libwebp.so.7', '/lib64/libwebp.so.7', 'BINARY'),
  ('libwebpmux.so.3', '/lib64/libwebpmux.so.3', 'BINARY'),
  ('liblcms2.so.2', '/lib64/liblcms2.so.2', 'BINARY'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('certifi/py.typed',
   '/usr/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('cryptography-44.0.3.dist-info/RECORD',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/RECORD',
   'DATA'),
  ('cryptography-44.0.3.dist-info/INSTALLER',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/INSTALLER',
   'DATA'),
  ('cryptography-44.0.3.dist-info/licenses/LICENSE',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/licenses/LICENSE',
   'DATA'),
  ('cryptography-44.0.3.dist-info/REQUESTED',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/REQUESTED',
   'DATA'),
  ('cryptography-44.0.3.dist-info/METADATA',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/METADATA',
   'DATA'),
  ('cryptography-44.0.3.dist-info/licenses/LICENSE.BSD',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/licenses/LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.3.dist-info/WHEEL',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/WHEEL',
   'DATA'),
  ('cryptography-44.0.3.dist-info/licenses/LICENSE.APACHE',
   '/usr/lib64/python3.13/site-packages/cryptography-44.0.3.dist-info/licenses/LICENSE.APACHE',
   'DATA'),
  ('h2-4.2.0.dist-info/top_level.txt',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/top_level.txt',
   'DATA'),
  ('h2-4.2.0.dist-info/METADATA',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/METADATA',
   'DATA'),
  ('h2-4.2.0.dist-info/REQUESTED',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/REQUESTED',
   'DATA'),
  ('h2-4.2.0.dist-info/LICENSE',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED',
   'DATA'),
  ('h2-4.2.0.dist-info/INSTALLER',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/INSTALLER',
   'DATA'),
  ('h2-4.2.0.dist-info/RECORD',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/METADATA',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/RECORD',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD',
   'DATA'),
  ('h2-4.2.0.dist-info/WHEEL',
   '/usr/lib/python3.13/site-packages/h2-4.2.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL',
   '/usr/lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL',
   'DATA'),
  ('base_library.zip',
   '/home/<USER>/Dropbox/剪切板/build/pastewith_pyperclip/base_library.zip',
   'DATA')],
 'libpython3.13.so.1.0',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
