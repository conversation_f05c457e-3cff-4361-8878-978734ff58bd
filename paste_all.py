import sys
import os
import platform
import subprocess
import shutil
import logging
from datetime import datetime
from pathlib import Path
import io
from flask import Flask, request, jsonify, send_file
from werkzeug.utils import secure_filename
import mimetypes

# 尝试导入平台特定的剪贴板库
try:
    if platform.system() == "Windows":
        import win32clipboard
        from PIL import Image
        CLIPBOARD_AVAILABLE = True
    elif platform.system() == "Darwin":  # macOS
        CLIPBOARD_AVAILABLE = True
    elif platform.system() == "Linux":
        # 检查是否有 xclip 或 xsel
        CLIPBOARD_AVAILABLE = shutil.which('xclip') is not None or shutil.which('xsel') is not None
    else:
        CLIPBOARD_AVAILABLE = False
except ImportError:
    CLIPBOARD_AVAILABLE = False

app = Flask(__name__)

# 配置
UPLOAD_FOLDER = 'uploads'
MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
ALLOWED_EXTENSIONS = {
    # 图片格式
    'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp',
    # 文档格式
    'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
    # 文本格式
    'txt', 'md', 'rtf',
    # 其他格式
    'zip', 'rar', '7z'
}

app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('paste_server.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)


def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def get_file_info(file_path):
    """获取文件信息"""
    try:
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'mime_type': mimetypes.guess_type(file_path)[0]
        }
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        return {}


def image_to_clipboard_windows(file_path):
    """Windows 系统将图片复制到剪贴板"""
    try:
        from PIL import Image
        image = Image.open(file_path)
        output = io.BytesIO()
        image.convert('RGB').save(output, 'BMP')
        data = output.getvalue()[14:]  # 去除BMP文件头
        output.close()
        
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
        win32clipboard.CloseClipboard()
        return True
    except Exception as e:
        logger.error(f"Windows 剪贴板操作失败: {e}")
        return False


def image_to_clipboard_macos(file_path):
    """macOS 系统将图片复制到剪贴板"""
    try:
        # 使用 osascript 将图片复制到剪贴板
        script = f'''
        set the clipboard to (read (POSIX file "{file_path}") as JPEG picture)
        '''
        subprocess.run(['osascript', '-e', script], check=True)
        return True
    except Exception as e:
        logger.error(f"macOS 剪贴板操作失败: {e}")
        return False


def image_to_clipboard_linux(file_path):
    """Linux 系统将图片复制到剪贴板"""
    try:
        # 尝试使用 xclip
        if shutil.which('xclip'):
            subprocess.run([
                'xclip', '-selection', 'clipboard', '-t', 'image/png', '-i', file_path
            ], check=True)
            return True
        # 尝试使用 xsel
        elif shutil.which('xsel'):
            with open(file_path, 'rb') as f:
                subprocess.run([
                    'xsel', '--clipboard', '--input'
                ], input=f.read(), check=True)
            return True
        else:
            logger.error("未找到 xclip 或 xsel")
            return False
    except Exception as e:
        logger.error(f"Linux 剪贴板操作失败: {e}")
        return False


def text_to_clipboard(text):
    """将文本复制到剪贴板"""
    try:
        system = platform.system()
        if system == "Windows":
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(text)
            win32clipboard.CloseClipboard()
        elif system == "Darwin":  # macOS
            subprocess.run(['pbcopy'], input=text.encode(), check=True)
        elif system == "Linux":
            if shutil.which('xclip'):
                subprocess.run(['xclip', '-selection', 'clipboard'], 
                             input=text.encode(), check=True)
            elif shutil.which('xsel'):
                subprocess.run(['xsel', '--clipboard', '--input'], 
                             input=text.encode(), check=True)
            else:
                return False
        return True
    except Exception as e:
        logger.error(f"文本剪贴板操作失败: {e}")
        return False


def extract_document_text(file_path, ext):
    """从文档文件中提取文本内容"""
    try:
        if ext == '.docx':
            return extract_docx_text(file_path)
        elif ext == '.doc':
            return extract_doc_text(file_path)
        elif ext in ['.pptx', '.ppt']:
            return extract_ppt_text(file_path)
        elif ext in ['.xlsx', '.xls']:
            return extract_excel_text(file_path)
        elif ext == '.pdf':
            return extract_pdf_text(file_path)
        else:
            return None
    except Exception as e:
        logger.error(f"提取文档内容失败 {file_path}: {e}")
        return None


def extract_docx_text(file_path):
    """提取 DOCX 文件的文本内容"""
    try:
        # 尝试使用 python-docx
        try:
            from docx import Document
            doc = Document(file_path)
            text_content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            return '\n'.join(text_content)
        except ImportError:
            logger.warning("未安装 python-docx，尝试使用 zipfile 方法")
            
        # 备用方法：直接解析 XML
        import zipfile
        import xml.etree.ElementTree as ET
        
        with zipfile.ZipFile(file_path, 'r') as docx:
            xml_content = docx.read('word/document.xml')
            root = ET.fromstring(xml_content)
            
            # 提取所有文本节点
            text_content = []
            for elem in root.iter():
                if elem.text:
                    text_content.append(elem.text)
            
            return ' '.join(text_content).strip()
            
    except Exception as e:
        logger.error(f"提取 DOCX 内容失败: {e}")
        return None


def extract_doc_text(file_path):
    """提取 DOC 文件的文本内容"""
    try:
        # 尝试使用 python-docx2txt
        try:
            import docx2txt
            return docx2txt.process(file_path)
        except ImportError:
            logger.warning("未安装 docx2txt")
            
        # 尝试使用 antiword (Linux/macOS)
        if shutil.which('antiword'):
            result = subprocess.run(['antiword', file_path], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
                
        return None
    except Exception as e:
        logger.error(f"提取 DOC 内容失败: {e}")
        return None


def extract_ppt_text(file_path):
    """提取 PPT/PPTX 文件的文本内容"""
    try:
        # 尝试使用 python-pptx
        try:
            from pptx import Presentation
            prs = Presentation(file_path)
            text_content = []
            
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text_content.append(shape.text.strip())
            
            return '\n'.join(text_content)
        except ImportError:
            logger.warning("未安装 python-pptx")
            return None
            
    except Exception as e:
        logger.error(f"提取 PPT 内容失败: {e}")
        return None


def extract_excel_text(file_path):
    """提取 Excel 文件的文本内容"""
    try:
        # 尝试使用 openpyxl (xlsx) 或 xlrd (xls)
        try:
            import openpyxl
            wb = openpyxl.load_workbook(file_path, data_only=True)
            text_content = []
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                text_content.append(f"=== {sheet_name} ===")
                
                for row in ws.iter_rows(values_only=True):
                    row_text = []
                    for cell in row:
                        if cell is not None:
                            row_text.append(str(cell))
                    if row_text:
                        text_content.append('\t'.join(row_text))
            
            return '\n'.join(text_content)
        except ImportError:
            logger.warning("未安装 openpyxl")
            
        # 尝试使用 pandas
        try:
            import pandas as pd
            df = pd.read_excel(file_path, sheet_name=None)
            text_content = []
            
            for sheet_name, sheet_df in df.items():
                text_content.append(f"=== {sheet_name} ===")
                text_content.append(sheet_df.to_string(index=False))
            
            return '\n'.join(text_content)
        except ImportError:
            logger.warning("未安装 pandas")
            return None
            
    except Exception as e:
        logger.error(f"提取 Excel 内容失败: {e}")
        return None


def extract_pdf_text(file_path):
    """提取 PDF 文件的文本内容"""
    try:
        # 尝试使用 PyPDF2
        try:
            import PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = []
                
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text.strip():
                        text_content.append(text.strip())
                
                return '\n'.join(text_content)
        except ImportError:
            logger.warning("未安装 PyPDF2")
            
        # 尝试使用 pdfplumber
        try:
            import pdfplumber
            with pdfplumber.open(file_path) as pdf:
                text_content = []
                for page in pdf.pages:
                    text = page.extract_text()
                    if text and text.strip():
                        text_content.append(text.strip())
                return '\n'.join(text_content)
        except ImportError:
            logger.warning("未安装 pdfplumber")
            return None
            
    except Exception as e:
        logger.error(f"提取 PDF 内容失败: {e}")
        return None


def copy_file_to_clipboard(file_path):
    """将文件本身复制到剪贴板"""
    try:
        system = platform.system()
        abs_path = os.path.abspath(file_path)
        
        if system == "Windows":
            return copy_file_to_clipboard_windows(abs_path)
        elif system == "Darwin":  # macOS
            return copy_file_to_clipboard_macos(abs_path)
        elif system == "Linux":
            return copy_file_to_clipboard_linux(abs_path)
        else:
            logger.error(f"不支持的操作系统: {system}")
            return False
            
    except Exception as e:
        logger.error(f"复制文件到剪贴板失败: {e}")
        return False


def copy_file_to_clipboard_windows(file_path):
    """Windows 系统将文件复制到剪贴板"""
    try:
        # 确保文件存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
            
        # 将文件路径转换为 Windows 格式，确保支持中文路径
        file_path = os.path.abspath(file_path).replace('/', '\\')
        
        # 确保路径是 Unicode 格式，支持中文
        if isinstance(file_path, bytes):
            file_path = file_path.decode('utf-8')
        
        logger.info(f"尝试复制文件到剪贴板: {file_path}")
        
        # 方法1: 使用 PowerShell (最可靠)
        try:
            ps_script = f'''
            Add-Type -AssemblyName System.Windows.Forms
            $files = New-Object System.Collections.Specialized.StringCollection
            $files.Add("{file_path}")
            [System.Windows.Forms.Clipboard]::SetFileDropList($files)
            Write-Output "Success"
            '''
            result = subprocess.run(['powershell', '-Command', ps_script], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and "Success" in result.stdout:
                logger.info(f"PowerShell 方法成功: 文件已复制到剪贴板")
                return True
            else:
                logger.warning(f"PowerShell 方法失败: {result.stderr}")
                
        except Exception as e:
            logger.warning(f"PowerShell 方法异常: {e}")
        
        # 方法2: 使用 win32clipboard 和正确的 HDROP 格式
        try:
            import win32clipboard
            import win32con
            import struct
            
            # 创建正确的 HDROP 结构
            # 文件路径必须以双空字符结尾
            file_list = file_path + '\0\0'
            file_list_bytes = file_list.encode('utf-16le')
            
            # HDROP 结构：
            # typedef struct _DROPFILES {
            #   DWORD pFiles;      // 文件列表偏移量
            #   POINT pt;          // 鼠标位置
            #   BOOL  fNC;         // 非客户区标志
            #   BOOL  fWide;       // Unicode 标志
            # } DROPFILES;
            
            header_size = 20  # DROPFILES 结构大小
            
            # 构建 DROPFILES 结构
            hdrop_data = struct.pack('<L', header_size)  # pFiles (偏移量)
            hdrop_data += struct.pack('<LL', 0, 0)       # pt.x, pt.y
            hdrop_data += struct.pack('<L', 0)           # fNC
            hdrop_data += struct.pack('<L', 1)           # fWide (Unicode)
            hdrop_data += file_list_bytes                # 文件路径列表
            
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32con.CF_HDROP, hdrop_data)
            win32clipboard.CloseClipboard()
            
            logger.info(f"win32clipboard 方法成功: 文件已复制到剪贴板")
            return True
            
        except Exception as e:
            logger.warning(f"win32clipboard 方法失败: {e}")
        
        # 方法3: 使用 ctypes 直接调用 Windows API
        try:
            import ctypes
            from ctypes import wintypes
            
            # 定义 Windows API
            kernel32 = ctypes.windll.kernel32
            user32 = ctypes.windll.user32
            
            # 分配全局内存
            file_list = file_path + '\0\0'
            file_list_bytes = file_list.encode('utf-16le')
            
            header_size = 20
            total_size = header_size + len(file_list_bytes)
            
            # 分配内存
            hMem = kernel32.GlobalAlloc(0x0002, total_size)  # GMEM_MOVEABLE
            if not hMem:
                raise Exception("GlobalAlloc 失败")
            
            # 锁定内存
            pMem = kernel32.GlobalLock(hMem)
            if not pMem:
                kernel32.GlobalFree(hMem)
                raise Exception("GlobalLock 失败")
            
            # 写入 DROPFILES 结构
            ctypes.memmove(pMem, struct.pack('<L', header_size), 4)
            ctypes.memmove(pMem + 4, struct.pack('<LL', 0, 0), 8)
            ctypes.memmove(pMem + 12, struct.pack('<L', 0), 4)
            ctypes.memmove(pMem + 16, struct.pack('<L', 1), 4)
            ctypes.memmove(pMem + header_size, file_list_bytes, len(file_list_bytes))
            
            # 解锁内存
            kernel32.GlobalUnlock(hMem)
            
            # 打开剪贴板
            if not user32.OpenClipboard(0):
                kernel32.GlobalFree(hMem)
                raise Exception("OpenClipboard 失败")
            
            # 清空剪贴板
            user32.EmptyClipboard()
            
            # 设置剪贴板数据
            CF_HDROP = 15
            if not user32.SetClipboardData(CF_HDROP, hMem):
                user32.CloseClipboard()
                kernel32.GlobalFree(hMem)
                raise Exception("SetClipboardData 失败")
            
            # 关闭剪贴板
            user32.CloseClipboard()
            
            logger.info(f"ctypes 方法成功: 文件已复制到剪贴板")
            return True
            
        except Exception as e:
            logger.warning(f"ctypes 方法失败: {e}")
        
        # 所有方法都失败，返回 False
        logger.error("所有 Windows 文件复制方法都失败")
        return False
        
    except Exception as e:
        logger.error(f"Windows 文件剪贴板操作异常: {e}")
        return False


def copy_file_to_clipboard_macos(file_path):
    """macOS 系统将文件复制到剪贴板"""
    try:
        # 使用 osascript 将文件复制到剪贴板
        script = f'''
        set the clipboard to (POSIX file "{file_path}")
        '''
        subprocess.run(['osascript', '-e', script], check=True)
        logger.info(f"macOS: 文件已复制到剪贴板: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"macOS 文件剪贴板操作失败: {e}")
        # 尝试备用方法
        try:
            # 使用 pbcopy 复制文件路径（作为 URI）
            file_uri = f"file://{file_path}"
            subprocess.run(['pbcopy'], input=file_uri.encode(), check=True)
            logger.info(f"macOS 备用方法: 文件URI已复制到剪贴板: {file_uri}")
            return True
        except Exception as e2:
            logger.error(f"macOS 备用方法也失败: {e2}")
            return False


def copy_file_to_clipboard_linux(file_path):
    """Linux 系统将文件复制到剪贴板"""
    try:
        # 创建文件URI格式
        file_uri = f"file://{file_path}"
        
        # 尝试使用 xclip 复制文件
        if shutil.which('xclip'):
            # 设置 MIME 类型为文件列表
            subprocess.run([
                'xclip', '-selection', 'clipboard', 
                '-t', 'text/uri-list'
            ], input=file_uri.encode(), check=True)
            logger.info(f"Linux xclip: 文件已复制到剪贴板: {file_path}")
            return True
            
        # 尝试使用 xsel
        elif shutil.which('xsel'):
            subprocess.run([
                'xsel', '--clipboard', '--input'
            ], input=file_uri.encode(), check=True)
            logger.info(f"Linux xsel: 文件已复制到剪贴板: {file_path}")
            return True
            
        else:
            logger.error("Linux: 未找到 xclip 或 xsel")
            return False
            
    except Exception as e:
        logger.error(f"Linux 文件剪贴板操作失败: {e}")
        return False


def file_to_clipboard(file_path):
    """将文件复制到剪贴板"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False, "文件不存在"
            
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # 图片文件处理
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp'}
        if ext in image_extensions:
            system = platform.system()
            if system == "Windows":
                success = image_to_clipboard_windows(file_path)
            elif system == "Darwin":
                success = image_to_clipboard_macos(file_path)
            elif system == "Linux":
                success = image_to_clipboard_linux(file_path)
            else:
                return False, f"不支持的操作系统: {system}"
                
            if success:
                logger.info(f"图片已复制到剪贴板: {file_path}")
                return True, "图片已成功复制到剪贴板"
            else:
                return False, "图片复制到剪贴板失败"
        
        # 文本文件处理
        text_extensions = {'.txt', '.md', '.rtf', '.log'}
        if ext in text_extensions:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                if text_to_clipboard(content):
                    logger.info(f"文本文件内容已复制到剪贴板: {file_path}")
                    return True, "文本内容已成功复制到剪贴板"
                else:
                    return False, "文本复制到剪贴板失败"
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                    if text_to_clipboard(content):
                        return True, "文本内容已成功复制到剪贴板"
                except:
                    return False, "文本文件编码不支持"
        
        # 文档文件处理 - 复制文件本身到剪贴板
        document_extensions = {'.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls', '.pdf'}
        if ext in document_extensions:
            success = copy_file_to_clipboard(file_path)
            if success:
                logger.info(f"文件已复制到剪贴板: {file_path}")
                return True, "文件已成功复制到剪贴板，可直接粘贴使用"
            else:
                # 备用方案：复制文件路径
                abs_path = os.path.abspath(file_path)
                if text_to_clipboard(abs_path):
                    logger.info(f"文件复制失败，已复制文件路径到剪贴板: {abs_path}")
                    return True, f"文件复制失败，已复制文件路径: {abs_path}"
                else:
                    return False, "文件处理失败"
        
        # 其他文件类型 - 复制文件路径
        abs_path = os.path.abspath(file_path)
        if text_to_clipboard(abs_path):
            logger.info(f"文件路径已复制到剪贴板: {abs_path}")
            return True, f"文件路径已复制到剪贴板: {abs_path}"
        else:
            return False, "文件路径复制失败"
            
    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")
        return False, f"处理文件时发生错误: {str(e)}"

@app.route('/', methods=['GET'])
def index():
    """主页 - 显示服务器状态和使用说明"""
    return jsonify({
        'service': 'Paste Server',
        'version': '2.0',
        'platform': platform.system(),
        'clipboard_available': CLIPBOARD_AVAILABLE,
        'endpoints': {
            'upload': '/upload - POST - 上传文件并复制到剪贴板',
            'files': '/files - GET - 列出已上传的文件',
            'download': '/download/<filename> - GET - 下载文件',
            'status': '/status - GET - 服务器状态'
        },
        'supported_formats': list(ALLOWED_EXTENSIONS)
    })


@app.route('/status', methods=['GET'])
def status():
    """获取服务器状态"""
    upload_count = len([f for f in os.listdir(UPLOAD_FOLDER) if os.path.isfile(os.path.join(UPLOAD_FOLDER, f))])
    upload_size = sum(os.path.getsize(os.path.join(UPLOAD_FOLDER, f)) 
                     for f in os.listdir(UPLOAD_FOLDER) 
                     if os.path.isfile(os.path.join(UPLOAD_FOLDER, f)))
    
    return jsonify({
        'status': 'running',
        'platform': platform.system(),
        'clipboard_available': CLIPBOARD_AVAILABLE,
        'upload_folder': UPLOAD_FOLDER,
        'uploaded_files_count': upload_count,
        'total_upload_size': upload_size,
        'max_file_size': MAX_CONTENT_LENGTH
    })


@app.route('/files', methods=['GET'])
def list_files():
    """列出已上传的文件"""
    try:
        files = []
        for filename in os.listdir(UPLOAD_FOLDER):
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            if os.path.isfile(file_path):
                file_info = get_file_info(file_path)
                files.append({
                    'filename': filename,
                    'size': file_info.get('size', 0),
                    'modified': file_info.get('modified', ''),
                    'mime_type': file_info.get('mime_type', 'unknown'),
                    'download_url': f'/download/{filename}'
                })
        
        return jsonify({
            'files': files,
            'total_count': len(files)
        })
    except Exception as e:
        logger.error(f"列出文件失败: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/download/<path:filename>', methods=['GET'])
def download_file(filename):
    """下载文件 - 支持中文文件名"""
    try:
        # 对于中文文件名，不使用 secure_filename
        # 直接检查文件是否存在于上传目录中
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        
        # 安全检查：确保文件路径在上传目录内
        upload_folder_abs = os.path.abspath(UPLOAD_FOLDER)
        file_path_abs = os.path.abspath(file_path)
        
        if not file_path_abs.startswith(upload_folder_abs):
            return jsonify({'error': '非法的文件路径'}), 400
        
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
            
        # 设置正确的 Content-Disposition 头部支持中文文件名
        from urllib.parse import quote
        encoded_filename = quote(filename.encode('utf-8'))
        
        response = send_file(file_path, as_attachment=True, download_name=filename)
        
        # 设置支持中文的文件名头部
        response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}'
        
        return response
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传请求"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
            
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
            
        # 处理中文文件名 - 不使用 secure_filename，它会移除中文字符
        filename = file.filename
        if not filename:
            return jsonify({'error': '无效的文件名'}), 400
            
        # 手动安全检查，保留中文字符
        # 移除危险字符但保留中文
        import re
        # 移除路径分隔符和其他危险字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除控制字符
        filename = re.sub(r'[\x00-\x1f\x7f]', '', filename)
        # 确保不是保留名称
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL'] + [f'COM{i}' for i in range(1, 10)] + [f'LPT{i}' for i in range(1, 10)]
        name_without_ext = os.path.splitext(filename)[0].upper()
        if name_without_ext in reserved_names:
            filename = f"file_{filename}"
            
        # 检查文件扩展名
        if not allowed_file(filename):
            return jsonify({
                'error': f'不支持的文件格式',
                'supported_formats': list(ALLOWED_EXTENSIONS)
            }), 400

        # 保存文件到上传目录
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        
        # 如果文件已存在，添加时间戳
        if os.path.exists(file_path):
            name, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(UPLOAD_FOLDER, filename)
        
        file.save(file_path)
        logger.info(f"文件已保存: {file_path}")
        
        # 获取文件信息
        file_info = get_file_info(file_path)
        
        # 尝试复制到剪贴板
        clipboard_result = False
        clipboard_message = "剪贴板功能不可用"
        
        if CLIPBOARD_AVAILABLE:
            clipboard_result, clipboard_message = file_to_clipboard(file_path)
        
        response_data = {
            'message': '文件上传成功',
            'filename': filename,
            'file_path': file_path,
            'file_info': file_info,
            'clipboard_success': clipboard_result,
            'clipboard_message': clipboard_message,
            'download_url': f'/download/{filename}'
        }
        
        if clipboard_result:
            logger.info(f"文件已上传并复制到剪贴板: {filename}")
            return jsonify(response_data), 200
        else:
            logger.warning(f"文件已上传但剪贴板操作失败: {filename} - {clipboard_message}")
            return jsonify(response_data), 200
            
    except Exception as e:
        logger.error(f"上传文件时发生错误: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/clipboard/text', methods=['POST'])
def clipboard_text():
    """将文本复制到剪贴板"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': '没有提供文本内容'}), 400
            
        text = data['text']
        if not CLIPBOARD_AVAILABLE:
            return jsonify({'error': '剪贴板功能不可用'}), 500
            
        if text_to_clipboard(text):
            logger.info("文本已复制到剪贴板")
            return jsonify({'message': '文本已成功复制到剪贴板'}), 200
        else:
            return jsonify({'error': '文本复制到剪贴板失败'}), 500
            
    except Exception as e:
        logger.error(f"复制文本到剪贴板失败: {e}")
        return jsonify({'error': str(e)}), 500


@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({'error': f'文件过大，最大允许 {MAX_CONTENT_LENGTH // (1024*1024)}MB'}), 413


@app.errorhandler(404)
def not_found(e):
    """404 错误处理"""
    return jsonify({'error': '请求的资源不存在'}), 404


@app.errorhandler(500)
def internal_error(e):
    """500 错误处理"""
    logger.error(f"内部服务器错误: {e}")
    return jsonify({'error': '内部服务器错误'}), 500


def main():
    """启动 Web 服务器"""
    logger.info("=" * 50)
    logger.info("Paste Server 启动中...")
    logger.info(f"平台: {platform.system()}")
    logger.info(f"剪贴板功能: {'可用' if CLIPBOARD_AVAILABLE else '不可用'}")
    logger.info(f"上传目录: {os.path.abspath(UPLOAD_FOLDER)}")
    logger.info(f"支持的文件格式: {', '.join(ALLOWED_EXTENSIONS)}")
    logger.info(f"最大文件大小: {MAX_CONTENT_LENGTH // (1024*1024)}MB")
    logger.info("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=12139, debug=False)
    except KeyboardInterrupt:
        logger.info("服务器被用户停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")


if __name__ == "__main__":
    main()