#include <iostream>
#include <string>
#include <vector>
#include <fstream>
#include <filesystem>
#include <thread>
#include <chrono>
#include <unordered_set>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <algorithm>

#ifdef _WIN32
#include <windows.h>
#include <wininet.h>
#pragma comment(lib, "wininet.lib")
#else
#include <curl/curl.h>
#include <X11/Xlib.h>
#include <X11/Xatom.h>
#endif

#include <openssl/md5.h>

// 配置信息
const std::string SERVER_IP = "************";
const std::string SERVER_PORT = "12139";

// 全局变量
std::string previousImageHash = "";
std::string previousDocxPath = "";
std::string previousTextContent = "";
std::string tempDir = std::filesystem::temp_directory_path();

// 支持的文档格式
std::unordered_set<std::string> supportedExtensions = {
    "docx", "doc", "ppt", "pptx", "xls", "xlsx", "pdf"
};

// 日志函数
void logInfo(const std::string& message) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    std::cout << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << " - INFO - " << message << std::endl;
}

void logError(const std::string& message) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    std::cerr << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << " - ERROR - " << message << std::endl;
}

// 计算文件MD5哈希值
std::string calculateMD5(const std::string& filePath) {
    std::ifstream file(filePath, std::ios::binary);
    if (!file.is_open()) {
        logError("无法打开文件: " + filePath);
        return "";
    }

    MD5_CTX md5Context;
    MD5_Init(&md5Context);

    char buffer[4096];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        MD5_Update(&md5Context, buffer, file.gcount());
    }

    unsigned char result[MD5_DIGEST_LENGTH];
    MD5_Final(result, &md5Context);

    std::stringstream ss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)result[i];
    }

    return ss.str();
}

#ifdef _WIN32
// Windows 实现
std::string getClipboardText() {
    if (!OpenClipboard(nullptr)) {
        return "";
    }

    HANDLE hData = GetClipboardData(CF_TEXT);
    if (hData == nullptr) {
        CloseClipboard();
        return "";
    }

    char* pszText = static_cast<char*>(GlobalLock(hData));
    if (pszText == nullptr) {
        CloseClipboard();
        return "";
    }

    std::string text(pszText);
    GlobalUnlock(hData);
    CloseClipboard();

    return text;
}

bool uploadFile(const std::string& filePath, const std::string& filename) {
    std::string uploadUrl = "http://" + SERVER_IP + ":" + SERVER_PORT + "/upload";
    
    HINTERNET hInternet = InternetOpenA("pastewith", INTERNET_OPEN_TYPE_DIRECT, nullptr, nullptr, 0);
    if (!hInternet) {
        logError("无法初始化WinINet");
        return false;
    }

    HINTERNET hConnect = InternetOpenUrlA(hInternet, uploadUrl.c_str(), nullptr, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hConnect) {
        logError("无法连接到服务器");
        InternetCloseHandle(hInternet);
        return false;
    }

    // 简化的上传实现，实际需要构造multipart/form-data
    logInfo("上传功能需要完整的HTTP multipart实现");
    
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);
    return true;
}

#else
// Linux 实现
std::string getClipboardText() {
    Display* display = XOpenDisplay(nullptr);
    if (!display) {
        return "";
    }

    Window window = DefaultRootWindow(display);
    Atom clipboard = XInternAtom(display, "CLIPBOARD", False);
    Atom utf8 = XInternAtom(display, "UTF8_STRING", False);
    Atom property = XInternAtom(display, "XSEL_DATA", False);

    XConvertSelection(display, clipboard, utf8, property, window, CurrentTime);
    XFlush(display);

    // 简化实现，实际需要处理SelectionNotify事件
    XCloseDisplay(display);
    return "";
}

// libcurl 回调函数
size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
    userp->append((char*)contents, size * nmemb);
    return size * nmemb;
}

bool uploadFile(const std::string& filePath, const std::string& filename) {
    CURL* curl;
    CURLcode res;
    std::string readBuffer;

    curl = curl_easy_init();
    if (!curl) {
        logError("无法初始化libcurl");
        return false;
    }

    std::string uploadUrl = "http://" + SERVER_IP + ":" + SERVER_PORT + "/upload";

    curl_mime* form = curl_mime_init(curl);
    curl_mimepart* field = curl_mime_addpart(form);
    
    curl_mime_name(field, "file");
    curl_mime_filedata(field, filePath.c_str());
    curl_mime_filename(field, filename.c_str());

    curl_easy_setopt(curl, CURLOPT_URL, uploadUrl.c_str());
    curl_easy_setopt(curl, CURLOPT_MIMEPOST, form);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);

    res = curl_easy_perform(curl);

    long response_code;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);

    curl_mime_free(form);
    curl_easy_cleanup(curl);

    if (res != CURLE_OK) {
        logError("发送失败：无法连接到服务器 " + SERVER_IP + ":" + SERVER_PORT + ", 错误: " + curl_easy_strerror(res));
        return false;
    }

    if (response_code == 200) {
        logInfo("上传成功: " + readBuffer);
        return true;
    } else {
        logError("上传失败，状态码: " + std::to_string(response_code) + ", 响应: " + readBuffer);
        return false;
    }
}
#endif

// URL解码
std::string urlDecode(const std::string& str) {
    std::string decoded;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            int value;
            std::istringstream is(str.substr(i + 1, 2));
            if (is >> std::hex >> value) {
                decoded += static_cast<char>(value);
                i += 2;
            } else {
                decoded += str[i];
            }
        } else if (str[i] == '+') {
            decoded += ' ';
        } else {
            decoded += str[i];
        }
    }
    return decoded;
}

// 从剪贴板文本内容解析文件路径
std::string parseFilePathFromClipboard(const std::string& textContent) {
    std::istringstream iss(textContent);
    std::string line;

    while (std::getline(iss, line)) {
        // 去除首尾空白
        line.erase(0, line.find_first_not_of(" \t\r\n"));
        line.erase(line.find_last_not_of(" \t\r\n") + 1);

        // 处理 file:// 协议的路径
        if (line.substr(0, 7) == "file://") {
            std::string filePath = line.substr(7); // 移除 file:// 前缀
            try {
                filePath = urlDecode(filePath);
                return filePath;
            } catch (const std::exception& e) {
                logError("URL解码错误: " + std::string(e.what()));
                continue;
            }
        }

        // 处理普通文件路径
        if (std::filesystem::exists(line)) {
            return line;
        }
    }

    return "";
}

// 处理文档剪贴板
void handleDocxClipboard() {
    std::string textContent = getClipboardText();

    if (textContent.empty() || textContent == previousTextContent) {
        return;
    }

    // 尝试解析文件路径
    std::string filePath = parseFilePathFromClipboard(textContent);

    if (filePath.empty()) {
        previousTextContent = textContent;
        return;
    }

    logInfo("检测到文件路径: " + filePath);

    // 检查文件是否存在
    if (!std::filesystem::exists(filePath)) {
        logError("文件不存在: " + filePath);
        previousTextContent = textContent;
        return;
    }

    // 检查文件扩展名
    std::string ext = std::filesystem::path(filePath).extension().string();
    if (!ext.empty() && ext[0] == '.') {
        ext = ext.substr(1);
    }
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    if (supportedExtensions.find(ext) == supportedExtensions.end()) {
        std::string supportedList;
        for (const auto& e : supportedExtensions) {
            if (!supportedList.empty()) supportedList += ", ";
            supportedList += e;
        }
        logInfo("不支持的文件类型: ." + ext + " (仅支持: " + supportedList + ")");
        previousTextContent = textContent;
        return;
    }

    logInfo("检测到支持的文件类型: ." + ext);

    // 检查是否是新文件
    if (filePath != previousDocxPath) {
        // 获取文件信息
        auto fileSize = std::filesystem::file_size(filePath);
        logInfo("文件大小: " + std::to_string(fileSize) + " bytes");

        // 为避免中文路径问题，复制到临时目录
        std::string filename = std::filesystem::path(filePath).filename().string();
        std::string tempFile = (std::filesystem::path(tempDir) / filename).string();

        logInfo("复制 '" + filePath + "' 到 '" + tempFile + "'");

        try {
            std::filesystem::copy_file(filePath, tempFile, std::filesystem::copy_options::overwrite_existing);

            // 上传文件
            if (uploadFile(tempFile, filename)) {
                previousDocxPath = filePath;
            }

            // 清理临时文件
            std::filesystem::remove(tempFile);

        } catch (const std::exception& e) {
            logError("复制文件失败: " + std::string(e.what()));
        }
    }

    previousTextContent = textContent;
}

// 处理图片剪贴板
void handleImageClipboard() {
    // 图片剪贴板处理需要平台特定实现
    // Windows: 使用 GetClipboardData(CF_BITMAP)
    // Linux: 使用 X11 或 Wayland 特定API
    logInfo("图片剪贴板处理需要平台特定实现");
}

// 检查剪贴板内容类型并处理
void checkClipboardContent() {
    // 检查图片
    handleImageClipboard();

    // 检查文档文件路径
    handleDocxClipboard();
}

int main() {
    logInfo("pastewith C++ 版本启动中...");
    logInfo("配置: 服务器 " + SERVER_IP + ":" + SERVER_PORT + ", 临时目录: " + tempDir);

    // 初始化剪贴板内容
    std::string previousContent = getClipboardText();

    logInfo("开始监控剪贴板变化...");

    // 主循环
    while (true) {
        try {
            // 检查剪贴板是否有变化
            std::string currentContent = getClipboardText();

            // 如果内容发生变化，处理剪贴板
            if (currentContent != previousContent) {
                logInfo("检测到剪贴板变化");
                checkClipboardContent();
                previousContent = currentContent;
            }

            // 检查图片剪贴板
            handleImageClipboard();

            // 短暂休眠避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::milliseconds(500));

        } catch (const std::exception& e) {
            logError("主循环错误: " + std::string(e.what()));
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    return 0;
}
