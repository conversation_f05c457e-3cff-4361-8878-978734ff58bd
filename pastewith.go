package main

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/atotto/clipboard"
	"github.com/kbinani/screenshot"
)

// 配置信息
const (
	SERVER_IP   = "************"
	SERVER_PORT = "12139"
)

// 全局变量
var (
	previousImageHash   = ""
	previousDocxPath    = ""
	previousTextContent = ""
	tempDir             = os.TempDir()
)

// 支持的文档格式
var supportedExtensions = map[string]bool{
	"docx": true,
	"doc":  true,
	"ppt":  true,
	"pptx": true,
	"xls":  true,
	"xlsx": true,
	"pdf":  true,
}

// calculateMD5 计算文件的MD5哈希值
func calculateMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// uploadFile 上传文件到服务器
func uploadFile(filePath, filename string) bool {
	if filename == "" {
		filename = filepath.Base(filePath)
	}

	uploadURL := fmt.Sprintf("http://%s:%s/upload", SERVER_IP, SERVER_PORT)

	file, err := os.Open(filePath)
	if err != nil {
		log.Printf("打开文件错误: %v", err)
		return false
	}
	defer file.Close()

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		log.Printf("创建表单文件错误: %v", err)
		return false
	}

	_, err = io.Copy(part, file)
	if err != nil {
		log.Printf("复制文件内容错误: %v", err)
		return false
	}

	writer.Close()

	req, err := http.NewRequest("POST", uploadURL, &buf)
	if err != nil {
		log.Printf("创建请求错误: %v", err)
		return false
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("发送失败：无法连接到服务器 %s:%s, 错误: %v", SERVER_IP, SERVER_PORT, err)
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("上传成功: %s", string(body))
		return true
	} else {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("上传失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
		return false
	}
}

// handleImageClipboard 处理图片剪贴板
func handleImageClipboard() {
	// 在 Linux 系统中，图片剪贴板监控需要使用 X11 API
	// 这里提供基础框架，可以通过 screenshot 库获取屏幕截图
	// 实际的剪贴板图片检测需要更复杂的 X11 集成

	// 检查是否有新的截图（简化实现）
	bounds := screenshot.GetDisplayBounds(0)
	if bounds.Dx() == 0 || bounds.Dy() == 0 {
		return
	}

	// 这里可以添加实际的剪贴板图片检测逻辑
	// 目前作为占位符实现
}

// parseFilePathFromClipboard 从剪贴板文本内容解析文件路径
func parseFilePathFromClipboard(textContent string) string {
	lines := strings.Split(strings.TrimSpace(textContent), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 处理 file:// 协议的路径
		if strings.HasPrefix(line, "file://") {
			filePath := line[7:] // 移除 file:// 前缀
			// URL 解码
			decoded, err := url.QueryUnescape(filePath)
			if err != nil {
				log.Printf("URL解码错误: %v", err)
				continue
			}
			return decoded
		}

		// 处理普通文件路径
		if _, err := os.Stat(line); err == nil {
			return line
		}
	}

	return ""
}

// handleDocxClipboard 处理文档剪贴板
func handleDocxClipboard() {
	textContent, err := clipboard.ReadAll()
	if err != nil {
		log.Printf("读取剪贴板错误: %v", err)
		return
	}

	if textContent == "" || textContent == previousTextContent {
		return
	}

	// 尝试解析文件路径
	filePath := parseFilePathFromClipboard(textContent)

	if filePath == "" {
		previousTextContent = textContent
		return
	}

	log.Printf("检测到文件路径: %s", filePath)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		log.Printf("文件不存在: %s", filePath)
		previousTextContent = textContent
		return
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(filePath))
	if strings.HasPrefix(ext, ".") {
		ext = ext[1:]
	}

	if !supportedExtensions[ext] {
		var supportedList []string
		for k := range supportedExtensions {
			supportedList = append(supportedList, k)
		}
		log.Printf("不支持的文件类型: .%s (仅支持: %s)", ext, strings.Join(supportedList, ", "))
		previousTextContent = textContent
		return
	}

	log.Printf("检测到支持的文件类型: .%s", ext)

	// 检查是否是新文件
	if filePath != previousDocxPath {
		// 获取文件信息
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			log.Printf("获取文件信息错误: %v", err)
			return
		}
		log.Printf("文件大小: %d bytes", fileInfo.Size())

		// 为避免中文路径问题，复制到临时目录
		filename := filepath.Base(filePath)
		tempFile := filepath.Join(tempDir, filename)

		log.Printf("复制 '%s' 到 '%s'", filePath, tempFile)

		// 复制文件
		src, err := os.Open(filePath)
		if err != nil {
			log.Printf("打开源文件失败: %v", err)
			return
		}
		defer src.Close()

		dst, err := os.Create(tempFile)
		if err != nil {
			log.Printf("创建临时文件失败: %v", err)
			return
		}
		defer dst.Close()

		_, err = io.Copy(dst, src)
		if err != nil {
			log.Printf("复制文件失败: %v", err)
			return
		}

		// 上传文件
		if uploadFile(tempFile, filename) {
			previousDocxPath = filePath
		}

		// 清理临时文件
		os.Remove(tempFile)
	}

	previousTextContent = textContent
}

// checkClipboardContent 检查剪贴板内容类型并处理
func checkClipboardContent() {
	// 检查图片
	handleImageClipboard()

	// 检查文档文件路径
	handleDocxClipboard()
}

func main() {
	log.Println("pastewith Go 版本启动中...")
	log.Printf("配置: 服务器 %s:%s, 临时目录: %s", SERVER_IP, SERVER_PORT, tempDir)

	// 初始化剪贴板内容
	previousContent, _ := clipboard.ReadAll()

	log.Println("开始监控剪贴板变化...")

	// 主循环
	for {
		// 检查剪贴板是否有变化
		currentContent, err := clipboard.ReadAll()
		if err != nil {
			log.Printf("读取剪贴板错误: %v", err)
			time.Sleep(1 * time.Second)
			continue
		}

		// 如果内容发生变化，处理剪贴板
		if currentContent != previousContent {
			log.Println("检测到剪贴板变化")
			checkClipboardContent()
			previousContent = currentContent
		}

		// 检查图片剪贴板
		handleImageClipboard()

		// 短暂休眠避免CPU占用过高
		time.Sleep(500 * time.Millisecond)
	}
}
