#!/bin/bash

# 服务器配置
serverIP="************"
serverPort="12139"
tempDir="/tmp"
previousImageHash=""
previousDocxPath=""

# 设置语言环境支持UTF-8
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# 创建 MD5 哈希函数
calculate_md5() {
  md5sum "$1" | awk '{print $1}'
}

# 处理图片剪贴板
handle_image_clipboard() {
  # 将图片从剪贴板保存到临时文件
  temp_image_file="${tempDir}/temp_clipboard_image.png"
  wl-paste --type image/png > "$temp_image_file"
  
  if [ $? -ne 0 ]; then
    echo "wl-paste 获取图片错误"
    return
  fi
  
  # 计算哈希值
  current_hash=$(calculate_md5 "$temp_image_file")
  
  if [ "$current_hash" != "$previousImageHash" ]; then
    echo "剪贴板更新：[截图/图像]"
    
    # 创建以哈希值命名的文件
    image_path="${tempDir}/${current_hash}.png"
    cp "$temp_image_file" "$image_path"
    
    # 上传图片
    echo "imagePath：$image_path"
    curl_cmd="curl -X POST -F file=@${image_path} http://${serverIP}:${serverPort}/upload"
    echo "curlCmd：$curl_cmd"
    
    upload_result=$(eval "$curl_cmd")
    
    if [ $? -ne 0 ]; then
      echo "发送失败：无法连接到服务器 ${serverIP}:${serverPort}"
    else
      echo "上传结果: $upload_result"
    fi
    
    previousImageHash="$current_hash"
  fi
  
  # 删除临时文件
  rm "$temp_image_file"
}

# 处理文档剪贴板
handle_docx_clipboard() {
  # 获取文件路径
  file_path=$(wl-paste --type text/uri-list)
  
  if [ $? -ne 0 ]; then
    echo "wl-paste 获取文件路径错误"
    return
  fi
  
  echo "原始filePath：$file_path"
  
  # 移除 file:// 前缀
  file_path=${file_path#file://}
  # 去除末尾的换行符和回车符
  file_path=${file_path%%$'\n'*}
  file_path=${file_path%%$'\r'*}
  # 去除可能的前导和尾随空格
  file_path=$(echo "$file_path" | xargs)
  
  # 处理URL编码的路径（解码%xx序列）
  file_path=$(echo -e "${file_path//%/\\x}")
  
  echo "处理后的docxPath：'$file_path'"
  
  # 检查文件扩展名
  file_ext="${file_path##*.}"
  file_ext=$(echo "$file_ext" | tr '[:upper:]' '[:lower:]')  # 转换为小写以匹配大小写不同的扩展名
  
  # 判断扩展名是否为支持的办公文档格式
  if [[ "$file_ext" != "docx" && "$file_ext" != "doc" && 
         "$file_ext" != "ppt" && "$file_ext" != "pptx" && 
         "$file_ext" != "xls" && "$file_ext" != "xlsx" && "$file_ext" != "pdf" ]]; then
    echo "不支持的文件类型: .$file_ext (仅支持: docx, doc, ppt, pptx, xls, xlsx)"
    return
  fi
  
  echo "检测到支持的文件类型: .$file_ext"
  
  # 输出文件信息以便调试
  if [ -e "$file_path" ]; then
    echo "文件确实存在"
    ls -la "$file_path"
  else
    echo "文件路径分析："
    echo "路径长度: ${#file_path}"
    echo "十六进制表示:"
    echo -n "$file_path" | hexdump -C
    
    # 尝试列出父目录内容
    parent_dir=$(dirname "$file_path")
    if [ -d "$parent_dir" ]; then
      echo "父目录存在，内容如下:"
      ls -la "$parent_dir"
    else
      echo "父目录不存在或无法访问"
    fi
  fi
  
  if [ "$file_path" != "$previousDocxPath" ]; then
    # 检查文件是否存在
    if [ ! -e "$file_path" ]; then
      echo "ERROR: 找不到文件 '$file_path'"
      return
    fi
    
    # 为避免中文路径问题，先复制到临时目录
    filename=$(basename "$file_path")
    temp_file="${tempDir}/${filename}"
    echo "复制 '$file_path' 到 '$temp_file'"
    
    # 使用单引号包裹路径以避免特殊字符问题
    cp "$file_path" "$temp_file"
    
    if [ $? -ne 0 ]; then
      echo "复制文件失败"
      return
    fi
    
    # 上传文件 - 使用临时文件
    curl_cmd="curl -X POST -F \"file=@${temp_file};filename=${filename}\" http://${serverIP}:${serverPort}/upload"
    echo "curlCmd：$curl_cmd"
    
    upload_result=$(eval "$curl_cmd")
    
    if [ $? -ne 0 ]; then
      echo "发送失败：无法连接到服务器 ${serverIP}:${serverPort}"
    else
      echo "上传结果: $upload_result"
    fi
    
    # 清理临时文件
    rm "$temp_file"
    
    previousDocxPath="$file_path"
  fi
}

# 主循环
while true; do
  # 等待剪贴板更新
  clipnotify
  
  if [ $? -ne 0 ]; then
    echo "clipnotify 错误"
    sleep 1
    continue
  fi
  
  # 检查剪贴板内容类型
  content_list=$(wl-paste --list)
  
  if [ $? -ne 0 ]; then
    echo "wl-paste --list 错误"
    continue
  fi
  
  # 处理图片
  if echo "$content_list" | grep -q "image/"; then
    handle_image_clipboard
  fi
  
  # 处理文档文件
  if echo "$content_list" | grep -q "application/vnd.portal.filetransfer"; then
    handle_docx_clipboard
  fi
done 