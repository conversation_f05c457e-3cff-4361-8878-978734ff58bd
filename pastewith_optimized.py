#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本的剪贴板监控工具
使用更高效的方式监控剪贴板变化，降低CPU占用
"""

import os
import sys
import time
import hashlib
import tempfile
import urllib.parse
from pathlib import Path
import requests
import pyperclip
from PIL import Image, ImageGrab
import io
import logging
import threading
import queue
import shutil

# 配置信息
SERVER_IP = "************"
SERVER_PORT = "12139"
TEMP_DIR = tempfile.gettempdir()

# 全局变量
previous_image_hash = ""
previous_docx_path = ""
previous_text_content = ""

# 支持的文档格式
SUPPORTED_EXTENSIONS = {
    'docx', 'doc', 'ppt', 'pptx', 
    'xls', 'xlsx', 'pdf'
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pastewith_optimized.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 任务队列
task_queue = queue.Queue()


def calculate_md5(file_path):
    """计算文件的MD5哈希值"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"计算MD5哈希值错误: {e}")
        return None


def upload_file(file_path, filename=None):
    """上传文件到服务器"""
    if filename is None:
        filename = os.path.basename(file_path)
    
    upload_url = f"http://{SERVER_IP}:{SERVER_PORT}/upload"
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (filename, f, 'application/octet-stream')}
            response = requests.post(upload_url, files=files, timeout=30)
            
        if response.status_code == 200:
            logger.info(f"上传成功: {response.text}")
            return True
        else:
            logger.error(f"上传失败，状态码: {response.status_code}, 响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"发送失败：无法连接到服务器 {SERVER_IP}:{SERVER_PORT}, 错误: {e}")
        return False
    except Exception as e:
        logger.error(f"上传文件时发生错误: {e}")
        return False


def process_image_task(image_data):
    """处理图片任务"""
    global previous_image_hash
    
    try:
        # 计算内存中图片数据的哈希值
        current_hash = hashlib.md5(image_data).hexdigest()
            
        if current_hash != previous_image_hash:
            logger.info("剪贴板更新：[截图/图像]")
            
            # 创建以哈希值命名的文件
            image_path = os.path.join(TEMP_DIR, f"{current_hash}.png")
            
            # 直接写入文件
            with open(image_path, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"imagePath: {image_path}")
            
            # 上传图片
            if upload_file(image_path, f"{current_hash}.png"):
                previous_image_hash = current_hash
            
            # 清理临时文件
            try:
                os.remove(image_path)
            except:
                pass
                
    except Exception as e:
        logger.error(f"处理图片任务错误: {e}")


def process_file_task(file_path):
    """处理文件任务"""
    global previous_docx_path
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return
            
        # 检查文件扩展名
        file_ext = Path(file_path).suffix.lower()
        if file_ext.startswith('.'):
            file_ext = file_ext[1:]
            
        if file_ext not in SUPPORTED_EXTENSIONS:
            logger.info(f"不支持的文件类型: .{file_ext}")
            return
            
        logger.info(f"检测到支持的文件类型: .{file_ext}")
        
        # 检查是否是新文件
        if file_path != previous_docx_path:
            # 获取文件信息
            file_stat = os.stat(file_path)
            logger.info(f"文件大小: {file_stat.st_size} bytes")
            
            # 检查文件大小
            MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
            if file_stat.st_size > MAX_FILE_SIZE:
                logger.warning(f"文件过大 ({file_stat.st_size} bytes)，跳过上传")
                return
            
            # 复制到临时目录
            filename = os.path.basename(file_path)
            temp_file = os.path.join(TEMP_DIR, filename)
            
            logger.info(f"复制 '{file_path}' 到 '{temp_file}'")
            shutil.copy2(file_path, temp_file)
            
            # 上传文件
            if upload_file(temp_file, filename):
                previous_docx_path = file_path
            
            # 清理临时文件
            try:
                os.remove(temp_file)
            except:
                pass
                
    except Exception as e:
        logger.error(f"处理文件任务错误: {e}")


def worker_thread():
    """工作线程，处理任务队列"""
    while True:
        try:
            task_type, task_data = task_queue.get(timeout=1)
            
            if task_type == "image":
                process_image_task(task_data)
            elif task_type == "file":
                process_file_task(task_data)
            elif task_type == "stop":
                break
                
            task_queue.task_done()
            
        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"工作线程错误: {e}")


def parse_file_path_from_clipboard(text_content):
    """从剪贴板文本内容解析文件路径"""
    lines = text_content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        
        # 处理 file:// 协议的路径
        if line.startswith('file://'):
            file_path = line[7:]  # 移除 file:// 前缀
            try:
                file_path = urllib.parse.unquote(file_path)
                return file_path
            except Exception as e:
                logger.error(f"URL解码错误: {e}")
                continue
        
        # 处理普通文件路径
        elif os.path.exists(line):
            return line
    
    return None


def monitor_clipboard():
    """监控剪贴板变化"""
    global previous_text_content
    
    logger.info("开始监控剪贴板变化...")
    
    # 初始化剪贴板内容
    try:
        previous_content = pyperclip.paste()
    except:
        previous_content = ""
    
    image_check_counter = 0
    IMAGE_CHECK_INTERVAL = 6  # 每3秒检查一次图片
    
    while True:
        try:
            # 检查文本剪贴板
            try:
                current_content = pyperclip.paste()
            except Exception as e:
                logger.debug(f"读取剪贴板文本失败: {e}")
                current_content = previous_content
            
            # 如果文本内容发生变化
            if current_content != previous_content:
                logger.debug("检测到剪贴板文本变化")
                
                # 尝试解析文件路径
                file_path = parse_file_path_from_clipboard(current_content)
                if file_path:
                    # 添加文件处理任务到队列
                    task_queue.put(("file", file_path))
                
                previous_content = current_content
                previous_text_content = current_content
            
            # 降低图片检查频率
            image_check_counter += 1
            if image_check_counter >= IMAGE_CHECK_INTERVAL:
                try:
                    image = ImageGrab.grabclipboard()
                    if image and isinstance(image, Image.Image):
                        # 在内存中处理图片
                        img_bytes = io.BytesIO()
                        image.save(img_bytes, format='PNG')
                        img_data = img_bytes.getvalue()
                        
                        # 添加图片处理任务到队列
                        task_queue.put(("image", img_data))
                        
                except Exception as e:
                    logger.debug(f"检查图片剪贴板失败: {e}")
                
                image_check_counter = 0
            
            # 休眠时间
            time.sleep(0.5)
            
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
            break
        except Exception as e:
            logger.error(f"监控循环错误: {e}")
            time.sleep(1)


def main():
    """主函数"""
    logger.info("pastewith Python 优化版本启动中...")
    logger.info(f"配置: 服务器 {SERVER_IP}:{SERVER_PORT}, 临时目录: {TEMP_DIR}")
    
    # 启动工作线程
    worker = threading.Thread(target=worker_thread, daemon=True)
    worker.start()
    
    try:
        # 开始监控剪贴板
        monitor_clipboard()
    finally:
        # 停止工作线程
        task_queue.put(("stop", None))
        worker.join(timeout=2)


if __name__ == "__main__":
    main()
