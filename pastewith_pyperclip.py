#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 pyperclip 实现 pastewith.go 的功能
监控剪贴板变化，自动上传图片和文档文件到服务器
"""

import os
import sys
import time
import hashlib
import tempfile
import urllib.parse
from pathlib import Path
import requests
import pyperclip
from PIL import Image, ImageGrab
import io
import logging

# 配置信息
SERVER_IP = "************"
SERVER_PORT = "12139"
TEMP_DIR = tempfile.gettempdir()

# 全局变量
previous_image_hash = ""
previous_docx_path = ""
previous_text_content = ""

# 支持的文档格式
SUPPORTED_EXTENSIONS = {
    'docx', 'doc', 'ppt', 'pptx', 
    'xls', 'xlsx', 'pdf'
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pastewith.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


def calculate_md5(file_path):
    """计算文件的MD5哈希值"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"计算MD5哈希值错误: {e}")
        return None


def upload_file(file_path, filename=None):
    """上传文件到服务器"""
    if filename is None:
        filename = os.path.basename(file_path)
    
    upload_url = f"http://{SERVER_IP}:{SERVER_PORT}/upload"
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (filename, f, 'application/octet-stream')}
            response = requests.post(upload_url, files=files, timeout=30)
            
        if response.status_code == 200:
            logger.info(f"上传成功: {response.text}")
            return True
        else:
            logger.error(f"上传失败，状态码: {response.status_code}, 响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"发送失败：无法连接到服务器 {SERVER_IP}:{SERVER_PORT}, 错误: {e}")
        return False
    except Exception as e:
        logger.error(f"上传文件时发生错误: {e}")
        return False


def handle_image_clipboard():
    """处理图片剪贴板"""
    global previous_image_hash

    try:
        # 尝试从剪贴板获取图片
        image = ImageGrab.grabclipboard()

        if image is None:
            return

        if not isinstance(image, Image.Image):
            return

        # 优化：直接在内存中计算哈希，避免临时文件
        img_bytes = io.BytesIO()
        image.save(img_bytes, format='PNG')
        img_data = img_bytes.getvalue()

        # 计算内存中图片数据的哈希值
        current_hash = hashlib.md5(img_data).hexdigest()

        if current_hash != previous_image_hash:
            logger.info("剪贴板更新：[截图/图像]")

            # 创建以哈希值命名的文件
            image_path = os.path.join(TEMP_DIR, f"{current_hash}.png")

            # 直接写入文件，避免重复保存
            with open(image_path, 'wb') as f:
                f.write(img_data)

            logger.info(f"imagePath: {image_path}")

            # 上传图片
            if upload_file(image_path, f"{current_hash}.png"):
                previous_image_hash = current_hash

            # 清理临时文件
            try:
                os.remove(image_path)
            except:
                pass

    except Exception as e:
        logger.error(f"处理图片剪贴板错误: {e}")


def parse_file_path_from_clipboard(text_content):
    """从剪贴板文本内容解析文件路径"""
    lines = text_content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        
        # 处理 file:// 协议的路径
        if line.startswith('file://'):
            file_path = line[7:]  # 移除 file:// 前缀
            # URL 解码
            try:
                file_path = urllib.parse.unquote(file_path)
                return file_path
            except Exception as e:
                logger.error(f"URL解码错误: {e}")
                continue
        
        # 处理普通文件路径
        elif os.path.exists(line):
            return line
    
    return None


def handle_docx_clipboard():
    """处理文档剪贴板"""
    global previous_docx_path, previous_text_content

    try:
        # 获取剪贴板文本内容
        text_content = pyperclip.paste()

        if not text_content or text_content == previous_text_content:
            return

        # 尝试解析文件路径
        file_path = parse_file_path_from_clipboard(text_content)

        if file_path is None:
            previous_text_content = text_content
            return

        logger.info(f"检测到文件路径: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            previous_text_content = text_content
            return

        # 检查文件扩展名
        file_ext = Path(file_path).suffix.lower()
        if file_ext.startswith('.'):
            file_ext = file_ext[1:]

        if file_ext not in SUPPORTED_EXTENSIONS:
            logger.info(f"不支持的文件类型: .{file_ext} (仅支持: {', '.join(SUPPORTED_EXTENSIONS)})")
            previous_text_content = text_content
            return

        logger.info(f"检测到支持的文件类型: .{file_ext}")

        # 检查是否是新文件
        if file_path != previous_docx_path:
            # 获取文件信息
            try:
                file_stat = os.stat(file_path)
                logger.info(f"文件大小: {file_stat.st_size} bytes")

                # 优化：检查文件大小，避免处理过大的文件
                MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
                if file_stat.st_size > MAX_FILE_SIZE:
                    logger.warning(f"文件过大 ({file_stat.st_size} bytes)，跳过上传")
                    previous_text_content = text_content
                    return

                # 为避免中文路径问题，复制到临时目录
                filename = os.path.basename(file_path)
                temp_file = os.path.join(TEMP_DIR, filename)

                logger.info(f"复制 '{file_path}' 到 '{temp_file}'")

                # 优化：使用 shutil.copy2 提高复制效率
                import shutil
                shutil.copy2(file_path, temp_file)

                # 上传文件
                if upload_file(temp_file, filename):
                    previous_docx_path = file_path

                # 清理临时文件
                try:
                    os.remove(temp_file)
                except:
                    pass

            except Exception as e:
                logger.error(f"处理文件失败: {e}")

        previous_text_content = text_content

    except Exception as e:
        logger.error(f"处理文档剪贴板错误: {e}")


def check_clipboard_content():
    """检查剪贴板内容类型并处理"""
    # 检查图片
    handle_image_clipboard()
    
    # 检查文档文件路径
    handle_docx_clipboard()


def main():
    """主函数"""
    logger.info("pastewith Python 版本启动中...")
    logger.info(f"配置: 服务器 {SERVER_IP}:{SERVER_PORT}, 临时目录: {TEMP_DIR}")

    # 初始化剪贴板内容
    try:
        previous_content = pyperclip.paste()
    except:
        previous_content = ""

    logger.info("开始监控剪贴板变化...")

    # 优化：减少图片检查频率
    image_check_counter = 0
    IMAGE_CHECK_INTERVAL = 4  # 每2秒检查一次图片（4 * 0.5s）

    # 主循环
    while True:
        try:
            # 检查剪贴板文本是否有变化
            try:
                current_content = pyperclip.paste()
            except Exception as e:
                logger.debug(f"读取剪贴板文本失败: {e}")
                current_content = previous_content

            # 如果内容发生变化，处理剪贴板
            if current_content != previous_content:
                logger.debug("检测到剪贴板文本变化")
                # 只处理文档剪贴板，避免重复处理图片
                handle_docx_clipboard()
                previous_content = current_content

            # 降低图片检查频率以减少CPU占用
            image_check_counter += 1
            if image_check_counter >= IMAGE_CHECK_INTERVAL:
                handle_image_clipboard()
                image_check_counter = 0

            # 增加休眠时间以降低CPU占用
            time.sleep(0.5)

        except KeyboardInterrupt:
            logger.info("程序被用户中断")
            break
        except Exception as e:
            logger.error(f"主循环错误: {e}")
            time.sleep(1)


if __name__ == "__main__":
    main()